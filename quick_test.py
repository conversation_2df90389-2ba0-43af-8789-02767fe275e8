#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复的关键部分
"""

import numpy as np
import scipy.io as sio
from glob import glob

# 测试安全标量提取函数
def safe_extract_scalar(data, key):
    """安全地从数据中提取标量值"""
    try:
        value = data[key]
        if hasattr(value, 'item'):
            return value.item()
        elif hasattr(value, 'flatten'):
            return value.flatten()[0]
        else:
            return value
    except Exception as e:
        print(f"提取{key}时出错: {e}")
        return None

def test_data_extraction():
    """测试数据提取"""
    # 找到第一个结果文件
    pattern = "**/cluster_*_results.mat"
    result_files = glob(pattern, recursive=True)
    
    if len(result_files) == 0:
        print("没有找到结果文件")
        return
    
    file_path = result_files[0]
    print(f"测试文件: {file_path}")
    
    try:
        data = sio.loadmat(file_path)
        
        # 测试提取cluster_id
        cluster_id = safe_extract_scalar(data, 'cluster_id')
        print(f"cluster_id: {cluster_id} (类型: {type(cluster_id)})")
        
        # 测试提取VAF
        vaf = safe_extract_scalar(data, 'VAF')
        print(f"VAF: {vaf} (类型: {type(vaf)})")
        
        # 测试权重数据
        if 'weights_pass1' in data:
            weights = data['weights_pass1']
            print(f"weights_pass1 类型: {type(weights)}")
            if hasattr(weights, '__len__') and len(weights) > 0:
                print(f"weights_pass1[0] 形状: {np.array(weights[0]).shape}")
        
        print("✅ 数据提取测试成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_extraction()
