#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试保存的RF数据结构
"""

import os
import numpy as np
import scipy.io as sio
from glob import glob

def analyze_data_structure(result_file):
    """分析单个结果文件的数据结构"""
    print(f"分析文件: {result_file}")
    print("=" * 60)
    
    try:
        data = sio.loadmat(result_file)
        
        # 显示所有键
        print("文件中的所有键:")
        for key in data.keys():
            if not key.startswith('__'):
                print(f"  {key}")
        print()
        
        # 分析主要数据结构
        main_keys = ['cluster_id', 'weights_pass1', 'weights_pass2', 'Final_Rf_Construct', 'VAF', 'gaussian_map']
        
        for key in main_keys:
            if key in data:
                value = data[key]
                print(f"{key}:")
                print(f"  类型: {type(value)}")
                
                if hasattr(value, 'shape'):
                    print(f"  形状: {value.shape}")
                elif hasattr(value, '__len__'):
                    print(f"  长度: {len(value)}")
                    if len(value) > 0:
                        print(f"  第一个元素类型: {type(value[0])}")
                        if hasattr(value[0], 'shape'):
                            print(f"  第一个元素形状: {value[0].shape}")
                
                if key == 'weights_pass1' and hasattr(value, '__len__') and len(value) > 0:
                    print(f"  详细分析weights_pass1:")
                    for i, weight in enumerate(value):
                        if hasattr(weight, 'shape'):
                            print(f"    权重[{i}]形状: {weight.shape}")
                        else:
                            print(f"    权重[{i}]类型: {type(weight)}")
                
                if key == 'weights_pass2' and hasattr(value, '__len__') and len(value) > 0:
                    print(f"  详细分析weights_pass2:")
                    for i, weight in enumerate(value):
                        if hasattr(weight, 'shape'):
                            print(f"    权重[{i}]形状: {weight.shape}")
                        else:
                            print(f"    权重[{i}]类型: {type(weight)}")
                
                print()
            else:
                print(f"{key}: 不存在")
                print()
        
        return data
        
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return None

def find_and_analyze_first_result():
    """找到并分析第一个结果文件"""
    pattern = "**/cluster_*_results.mat"
    result_files = glob(pattern, recursive=True)
    
    if len(result_files) == 0:
        print("没有找到结果文件")
        return None
    
    print(f"找到 {len(result_files)} 个结果文件")
    print(f"分析第一个文件: {result_files[0]}")
    print()
    
    return analyze_data_structure(result_files[0])

def test_weight_extraction(data):
    """测试权重提取的不同方法"""
    if data is None:
        return
    
    print("=" * 60)
    print("测试权重提取方法")
    print("=" * 60)
    
    if 'weights_pass1' in data:
        weights_pass1 = data['weights_pass1']
        print(f"weights_pass1 原始类型: {type(weights_pass1)}")
        
        # 方法1: 直接索引
        try:
            result1 = weights_pass1[0]
            print(f"方法1 - weights_pass1[0]: 成功, 形状={result1.shape if hasattr(result1, 'shape') else 'N/A'}")
        except Exception as e:
            print(f"方法1 - weights_pass1[0]: 失败, {e}")
        
        # 方法2: 转换为numpy数组后索引
        try:
            weights_array = np.array(weights_pass1)
            print(f"转换为numpy数组: 形状={weights_array.shape}")
            if weights_array.ndim > 0:
                result2 = weights_array[0]
                print(f"方法2 - np.array(weights_pass1)[0]: 成功, 形状={result2.shape if hasattr(result2, 'shape') else 'N/A'}")
        except Exception as e:
            print(f"方法2 - np.array(weights_pass1)[0]: 失败, {e}")
        
        # 方法3: 检查是否是对象数组
        try:
            if hasattr(weights_pass1, 'dtype') and weights_pass1.dtype == 'object':
                print("weights_pass1 是对象数组")
                result3 = weights_pass1.item(0)
                print(f"方法3 - weights_pass1.item(0): 成功, 形状={result3.shape if hasattr(result3, 'shape') else 'N/A'}")
        except Exception as e:
            print(f"方法3 - weights_pass1.item(0): 失败, {e}")

def create_safe_extraction_function():
    """创建安全的权重提取函数"""
    print("=" * 60)
    print("推荐的安全权重提取函数")
    print("=" * 60)
    
    code = '''
def safe_extract_filter_weights(weights_pass1):
    """安全地提取滤波器权重"""
    try:
        # 方法1: 直接索引
        if hasattr(weights_pass1, '__getitem__'):
            filter_data = weights_pass1[0]
            if hasattr(filter_data, 'shape'):
                if filter_data.ndim == 4:
                    return filter_data[:, :, :, 0]
                elif filter_data.ndim == 3:
                    return filter_data
                else:
                    return filter_data
        
        # 方法2: 对象数组
        if hasattr(weights_pass1, 'dtype') and weights_pass1.dtype == 'object':
            filter_data = weights_pass1.item(0)
            if hasattr(filter_data, 'shape'):
                if filter_data.ndim == 4:
                    return filter_data[:, :, :, 0]
                elif filter_data.ndim == 3:
                    return filter_data
                else:
                    return filter_data
        
        # 方法3: 转换为numpy数组
        weights_array = np.array(weights_pass1)
        if weights_array.ndim > 0:
            filter_data = weights_array[0]
            if hasattr(filter_data, 'shape'):
                if filter_data.ndim == 4:
                    return filter_data[:, :, :, 0]
                elif filter_data.ndim == 3:
                    return filter_data
                else:
                    return filter_data
        
        return None
        
    except Exception as e:
        print(f"提取滤波器权重时出错: {e}")
        return None
'''
    
    print(code)
    return code

def main():
    """主函数"""
    print("RF数据结构调试工具")
    print("=" * 60)
    
    # 分析第一个结果文件
    data = find_and_analyze_first_result()
    
    if data is not None:
        # 测试权重提取
        test_weight_extraction(data)
        
        # 显示推荐的提取函数
        create_safe_extraction_function()
    
    print("\n调试完成！")

if __name__ == "__main__":
    main()
