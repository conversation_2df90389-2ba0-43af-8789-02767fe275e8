#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的RF绘制脚本
"""

import sys
import traceback

def test_import():
    """测试导入"""
    try:
        from plot_rf_from_saved_data import (
            find_result_files, 
            load_cluster_results, 
            safe_extract_scalar,
            safe_extract_filter_weights
        )
        print("✅ 成功导入所有函数")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_find_files():
    """测试查找文件"""
    try:
        from plot_rf_from_saved_data import find_result_files
        
        files = find_result_files(".")
        print(f"找到 {len(files)} 个结果文件")
        
        if len(files) > 0:
            print(f"第一个文件: {files[0]}")
            return files[0]
        else:
            print("没有找到结果文件")
            return None
            
    except Exception as e:
        print(f"❌ 查找文件失败: {e}")
        traceback.print_exc()
        return None

def test_load_data(file_path):
    """测试加载数据"""
    if file_path is None:
        return None
        
    try:
        from plot_rf_from_saved_data import load_cluster_results, safe_extract_scalar
        
        data = load_cluster_results(file_path)
        if data is None:
            print("❌ 无法加载数据")
            return None
            
        print("✅ 成功加载数据")
        
        # 测试标量提取
        try:
            cluster_id = int(safe_extract_scalar(data, 'cluster_id'))
            vaf = float(safe_extract_scalar(data, 'VAF'))
            print(f"✅ 成功提取: Cluster {cluster_id}, VAF {vaf:.2f}%")
            return data
        except Exception as e:
            print(f"❌ 提取标量失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        traceback.print_exc()
        return None

def test_weight_extraction(data):
    """测试权重提取"""
    if data is None:
        return False
        
    try:
        from plot_rf_from_saved_data import safe_extract_filter_weights
        
        weights_pass1 = data['weights_pass1']
        filterWeights = safe_extract_filter_weights(weights_pass1)
        
        if filterWeights is not None:
            print(f"✅ 成功提取滤波器权重: 形状 {filterWeights.shape}")
            return True
        else:
            print("❌ 无法提取滤波器权重")
            return False
            
    except Exception as e:
        print(f"❌ 权重提取失败: {e}")
        traceback.print_exc()
        return False

def test_single_plot(data):
    """测试单个绘图"""
    if data is None:
        return False
        
    try:
        from plot_rf_from_saved_data import plot_rf_components, safe_extract_scalar
        
        cluster_id = int(safe_extract_scalar(data, 'cluster_id'))
        print(f"尝试绘制 Cluster {cluster_id}...")
        
        save_dir = plot_rf_components(cluster_id, data)
        print(f"✅ 成功绘制，保存到: {save_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 绘制失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("测试修复后的RF绘制脚本")
    print("=" * 50)
    
    # 1. 测试导入
    if not test_import():
        return
    
    # 2. 测试查找文件
    file_path = test_find_files()
    
    # 3. 测试加载数据
    data = test_load_data(file_path)
    
    # 4. 测试权重提取
    if not test_weight_extraction(data):
        return
    
    # 5. 测试绘图
    if test_single_plot(data):
        print("\n✅ 所有测试通过！脚本修复成功")
    else:
        print("\n❌ 绘图测试失败")

if __name__ == "__main__":
    main()
