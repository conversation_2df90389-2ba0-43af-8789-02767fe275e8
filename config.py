#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
神经元数据处理配置文件
"""

# 数据文件配置
DATA_CONFIG = {
    # 原始神经元数据文件
    'origin_neu_file': 'PLCATD001_Ephys_DA07_Image_0.mat',

    # 原始刺激图像文件
    'origin_img_file': 'Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35).mat',

    # 处理后的刺激数据文件
    'stimuli_file': 'DataSets_ZHJ2024_30_averaged.mat',

    # 数据集名称（从origin_neu_file自动提取，也可手动指定）
    'dataset_name': None,  # None表示自动从文件名提取
}

# SystemIdentification算法参数
SYSID_CONFIG = {
    # 图像裁剪参数
    'crop_x1': 0,
    'crop_x2': 30,
    'crop_y1': 0,
    'crop_y2': 30,

    # 时间相关参数
    'num_timelags': 7,  # 时间滞后数量
    'time_window': 120,  # 时间窗口大小(ms)

    # 卷积网络参数
    'Stride': 1,  # 步长
    'Filter_Size': 15,  # 滤波器大小
    'Pool_Size': 1,  # 池化大小

    # 训练参数
    'epochs_pass1': 100,  # 第一阶段训练轮数
    'epochs_pass2': 100,  # 第二阶段训练轮数
    'batch_size': 32,  # 批次大小
    'initial_exp': 2.0,  # 初始功率律指数
}

# 数据分割参数
DATA_SPLIT_CONFIG = {
    # 训练集结束索引
    'train_end': 16800,

    # 验证集结束索引
    'validation_end': 19200,

    # 训练集重复次数
    'train_repeats': 5,

    # 验证和测试集重复次数
    'val_test_repeats': 20,
}

# 输出配置
OUTPUT_CONFIG = {
    # 是否保存图像结果
    'save_plots': True,

    # 图像分辨率
    'plot_dpi': 300,

    # 是否显示详细进度
    'verbose': True,

    # 是否保存中间结果
    'save_intermediate': True,

    # 结果目录前缀
    'data_dir_prefix': './PLCATD001_Ephys_DA07_Image_0/data/cluster',
    'results_dir_prefix': './PLCATD001_Ephys_DA07_Image_0/results/cluster',

    # 结果文件后缀
    'data_dir_suffix': '_data',
    'results_dir_suffix': '_results',
}

# 处理选项
PROCESSING_CONFIG = {
    # 是否跳过已存在的结果
    'skip_existing': True,

    # 是否并行处理（暂未实现）
    'parallel_processing': False,

    # 最大并行进程数
    'max_workers': 4,

    # 是否在出错时继续处理其他cluster
    'continue_on_error': True,

    # 是否保存失败的cluster信息
    'save_failed_info': True,
}

# 质量控制参数
QUALITY_CONFIG = {
    # 最小VAF阈值（低于此值的结果会被标记）
    'min_vaf_threshold': 5.0,

    # 最小spike数量阈值
    'min_spike_count': 100,

    # 是否过滤低质量结果
    'filter_low_quality': False,
}

# Cluster筛选参数
CLUSTER_FILTER_CONFIG = {
    # 是否启用深度筛选
    'enable_depth_filter': True,

    # 最大深度阈值（微米）
    'max_depth_threshold': 600,

    # 是否启用GoodCluster筛选
    'enable_good_filter': False,

    # 是否显示筛选详情
    'show_filter_details': True,
}


def get_config():
    """获取完整配置"""
    return {
        'data': DATA_CONFIG,
        'sysid': SYSID_CONFIG,
        'data_split': DATA_SPLIT_CONFIG,
        'output': OUTPUT_CONFIG,
        'processing': PROCESSING_CONFIG,
        'quality': QUALITY_CONFIG,
        'cluster_filter': CLUSTER_FILTER_CONFIG,
    }


def print_config():
    """打印当前配置"""
    config = get_config()

    print("=" * 60)
    print("当前配置参数")
    print("=" * 60)

    for section_name, section_config in config.items():
        print(f"\n[{section_name.upper()}]")
        for key, value in section_config.items():
            print(f"  {key}: {value}")


def validate_config():
    """验证配置参数"""
    config = get_config()
    errors = []

    # 验证数据配置
    data_config = config['data']
    if not data_config['origin_neu_file']:
        errors.append("origin_neu_file 不能为空")

    if not data_config['origin_img_file']:
        errors.append("origin_img_file 不能为空")

    # 验证SystemID配置
    sysid_config = config['sysid']
    if sysid_config['crop_x2'] <= sysid_config['crop_x1']:
        errors.append("crop_x2 必须大于 crop_x1")

    if sysid_config['crop_y2'] <= sysid_config['crop_y1']:
        errors.append("crop_y2 必须大于 crop_y1")

    if sysid_config['num_timelags'] <= 0:
        errors.append("num_timelags 必须大于 0")

    if sysid_config['Filter_Size'] <= 0:
        errors.append("Filter_Size 必须大于 0")

    # 验证数据分割配置
    split_config = config['data_split']
    if split_config['validation_end'] <= split_config['train_end']:
        errors.append("validation_end 必须大于 train_end")

    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  ❌ {error}")
        return False
    else:
        print("✅ 配置验证通过")
        return True


if __name__ == "__main__":
    print_config()
    print("\n")
    validate_config()
