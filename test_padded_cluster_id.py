#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试补零的cluster_id格式
"""

import os

def test_cluster_id_formatting():
    """测试cluster_id格式化"""
    print("=" * 50)
    print("测试cluster_id补零格式")
    print("=" * 50)
    
    # 测试不同的cluster_id
    test_ids = [1, 23, 456, 7890]
    
    print("原始ID -> 补零格式:")
    for cluster_id in test_ids:
        padded = f"{cluster_id:03d}"
        print(f"  {cluster_id:4d} -> {padded}")
    
    print("\n生成的路径示例:")
    base_path = "./PLCATD001_Ephys_DA07_Image_0/SingleUnit"
    
    for cluster_id in test_ids:
        padded = f"{cluster_id:03d}"
        
        # 数据路径
        data_path = f"{base_path}/data/cluster_{padded}_data"
        print(f"数据路径: {data_path}")
        
        # 结果路径
        results_path = f"{base_path}/results/cluster_{padded}_results"
        print(f"结果路径: {results_path}")
        
        # 文件名示例
        mat_file = f"cluster_{padded}_results.mat"
        png_file = f"cluster_{padded}_learning_curve.png"
        print(f"MAT文件: {mat_file}")
        print(f"PNG文件: {png_file}")
        print()

def test_file_pattern_matching():
    """测试文件模式匹配"""
    print("=" * 50)
    print("测试文件模式匹配")
    print("=" * 50)
    
    from glob import glob
    
    # 模拟一些文件名
    test_files = [
        "cluster_001_results.mat",
        "cluster_023_results.mat", 
        "cluster_456_results.mat",
        "cluster_1_results.mat",  # 旧格式
        "cluster_23_results.mat", # 旧格式
        "other_file.mat"
    ]
    
    print("测试文件列表:")
    for f in test_files:
        print(f"  {f}")
    
    # 测试模式
    patterns = [
        "cluster_[0-9][0-9][0-9]_results.mat",  # 三位数
        "cluster_*_results.mat",                # 通配符
    ]
    
    print("\n模式匹配测试:")
    for pattern in patterns:
        print(f"\n模式: {pattern}")
        # 这里只是演示，实际使用时需要真实文件
        import re
        regex_pattern = pattern.replace("*", ".*").replace("[0-9]", r"\d")
        regex_pattern = "^" + regex_pattern + "$"
        
        for f in test_files:
            if re.match(regex_pattern, f):
                print(f"  匹配: {f}")

def show_directory_structure():
    """显示预期的目录结构"""
    print("=" * 50)
    print("预期的目录结构")
    print("=" * 50)
    
    structure = """
PLCATD001_Ephys_DA07_Image_0/
└── SingleUnit/
    ├── data/
    │   ├── cluster_001_data/
    │   │   ├── PLCATD001_Ephys_DA07_Image_0_estSetResp.mat
    │   │   ├── PLCATD001_Ephys_DA07_Image_0_regSetResp.mat
    │   │   ├── PLCATD001_Ephys_DA07_Image_0_predSetResp.mat
    │   │   └── PLCATD001_Ephys_DA07_Image_0_averaged_responses.mat
    │   ├── cluster_002_data/
    │   └── cluster_468_data/
    └── results/
        ├── cluster_001_results/
        │   ├── cluster_001_results.mat
        │   ├── cluster_001_learning_curve.png
        │   ├── cluster_001_prelu_alpha.png
        │   ├── cluster_001_gaussian_map.png
        │   ├── cluster_001_rf_filter_weights.png
        │   └── cluster_001_reconstructed_filter.png
        ├── cluster_002_results/
        └── cluster_468_results/
            ├── cluster_468_results.mat
            ├── cluster_468_learning_curve.png
            ├── cluster_468_prelu_alpha.png
            ├── cluster_468_gaussian_map.png
            ├── cluster_468_rf_filter_weights.png
            └── cluster_468_reconstructed_filter.png
    """
    
    print(structure)

def main():
    """主函数"""
    print("Cluster ID补零格式测试")
    print("=" * 60)
    
    test_cluster_id_formatting()
    test_file_pattern_matching()
    show_directory_structure()
    
    print("=" * 60)
    print("修改总结:")
    print("1. 所有cluster_id现在都会补零到三位数")
    print("2. 目录名: cluster_001_data, cluster_468_results")
    print("3. 文件名: cluster_001_results.mat, cluster_468_learning_curve.png")
    print("4. plot_rf_from_saved_data.py已更新以支持新格式")
    print("5. 同时保持对旧格式的兼容性")
    print("=" * 60)

if __name__ == "__main__":
    main()
