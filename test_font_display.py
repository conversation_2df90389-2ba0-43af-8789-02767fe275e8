#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体显示
"""

import matplotlib.pyplot as plt
import numpy as np

def test_font_display():
    """测试字体显示"""
    print("测试matplotlib字体显示...")
    
    # 设置字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建测试图
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # 测试1: 中文标题
    axes[0, 0].plot([1, 2, 3], [1, 4, 2])
    axes[0, 0].set_title('中文标题测试')
    axes[0, 0].set_xlabel('X轴标签')
    axes[0, 0].set_ylabel('Y轴标签')
    
    # 测试2: 英文标题
    axes[0, 1].plot([1, 2, 3], [2, 1, 3])
    axes[0, 1].set_title('English Title Test')
    axes[0, 1].set_xlabel('X Label')
    axes[0, 1].set_ylabel('Y Label')
    
    # 测试3: 混合标题
    axes[1, 0].plot([1, 2, 3], [3, 2, 1])
    axes[1, 0].set_title('Mixed 混合 Title')
    axes[1, 0].set_xlabel('混合 Label')
    axes[1, 0].set_ylabel('Y轴')
    
    # 测试4: RF相关术语
    data = np.random.randn(10, 10)
    im = axes[1, 1].imshow(data, cmap='RdBu_r')
    axes[1, 1].set_title('Receptive Field (感受野)')
    plt.colorbar(im, ax=axes[1, 1])
    
    plt.tight_layout()
    plt.savefig('font_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("字体测试完成！")
    print("如果标题显示为方框，说明中文字体不可用")
    print("建议:")
    print("1. 安装中文字体（如SimHei、Microsoft YaHei）")
    print("2. 或者使用英文标题")

def list_available_fonts():
    """列出可用字体"""
    try:
        import matplotlib.font_manager as fm
        
        print("系统中可用的字体:")
        fonts = [f.name for f in fm.fontManager.ttflist]
        chinese_fonts = [f for f in fonts if any(keyword in f for keyword in ['Sim', 'Microsoft', 'Kai', 'Fang', 'Hei', 'Song'])]
        
        print("可能的中文字体:")
        for font in sorted(set(chinese_fonts)):
            print(f"  - {font}")
            
        if not chinese_fonts:
            print("  未找到中文字体")
            
    except Exception as e:
        print(f"无法列出字体: {e}")

if __name__ == "__main__":
    list_available_fonts()
    print()
    test_font_display()
