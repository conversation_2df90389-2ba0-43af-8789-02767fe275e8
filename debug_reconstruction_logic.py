#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试重构逻辑
"""

import numpy as np
import matplotlib.pyplot as plt

def manual_reconstruction(filterWeights, mapWeights, stride, poolSize, fullSize):
    """手动实现重构逻辑，便于调试"""
    mapSize = mapWeights.shape[0]
    filterSize = filterWeights.shape[0]
    numLags = filterWeights.shape[2]
    
    print(f"重构参数:")
    print(f"  mapSize: {mapSize}")
    print(f"  filterSize: {filterSize}")
    print(f"  numLags: {numLags}")
    print(f"  stride: {stride}")
    print(f"  poolSize: {poolSize}")
    print(f"  fullSize: {fullSize}")
    
    # 第一步：创建unPoolFilter
    unPoolSize = mapSize * poolSize * stride
    unPoolFilter = np.zeros((unPoolSize, unPoolSize))
    
    print(f"\n第一步：创建unPoolFilter，大小: {unPoolSize}x{unPoolSize}")
    
    for map_y_idx in range(mapSize):
        for map_x_idx in range(mapSize):
            y_start = map_y_idx * poolSize * stride
            y_end = (map_y_idx + 1) * poolSize * stride
            x_start = map_x_idx * poolSize * stride
            x_end = (map_x_idx + 1) * poolSize * stride
            
            weight_value = mapWeights[map_y_idx, map_x_idx]
            unPoolFilter[y_start:y_end, x_start:x_end] += weight_value
            
            print(f"  map[{map_y_idx},{map_x_idx}]={weight_value:.2f} -> unPool[{y_start}:{y_end},{x_start}:{x_end}]")
    
    print(f"\nunPoolFilter:")
    print(unPoolFilter)
    
    # 第二步：重构滤波器
    reconFilter = np.zeros((fullSize, fullSize, numLags))
    
    print(f"\n第二步：重构滤波器")
    
    for lag in range(numLags):
        print(f"\n处理lag {lag}:")
        for y_idx in range(unPoolSize):
            for x_idx in range(unPoolSize):
                if unPoolFilter[y_idx, x_idx] != 0:
                    y_start = y_idx
                    y_end = min(y_idx + filterSize, fullSize)
                    x_start = x_idx
                    x_end = min(x_idx + filterSize, fullSize)
                    
                    filter_y_end = y_end - y_start
                    filter_x_end = x_end - x_start
                    
                    if filter_y_end > 0 and filter_x_end > 0:
                        weight = unPoolFilter[y_idx, x_idx]
                        filter_patch = filterWeights[:filter_y_end, :filter_x_end, lag]
                        
                        reconFilter[y_start:y_end, x_start:x_end, lag] += weight * filter_patch
                        
                        print(f"    位置[{y_idx},{x_idx}] 权重={weight:.2f} -> recon[{y_start}:{y_end},{x_start}:{x_end}]")
    
    return reconFilter, unPoolFilter

def test_simple_case():
    """测试简单情况"""
    print("=" * 60)
    print("测试简单重构情况")
    print("=" * 60)
    
    # 创建简单的3x3滤波器
    filterSize = 3
    numLags = 1
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    
    # 创建一个简单的十字形
    filterWeights[1, :, 0] = 1.0  # 水平线
    filterWeights[:, 1, 0] = 1.0  # 垂直线
    
    print("原始滤波器（十字形）:")
    print(filterWeights[:, :, 0])
    
    # 简单的2x2映射
    mapWeights = np.array([[1.0, 0.0],
                          [0.0, 1.0]])
    
    print("\n映射权重:")
    print(mapWeights)
    
    # 重构参数
    stride = 2
    poolSize = 1
    fullSize = 8
    
    # 手动重构
    reconFilter, unPoolFilter = manual_reconstruction(filterWeights, mapWeights, stride, poolSize, fullSize)
    
    # 显示结果
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original Filter (Cross)')
    plt.colorbar()
    
    plt.subplot(1, 3, 2)
    plt.imshow(unPoolFilter, cmap='hot', interpolation='nearest')
    plt.title('UnPool Filter')
    plt.colorbar()
    
    plt.subplot(1, 3, 3)
    plt.imshow(reconFilter[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed Filter')
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('debug_reconstruction.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"\n最终重构结果:")
    print(reconFilter[:, :, 0])
    
    # 分析结果
    frame = reconFilter[:, :, 0]
    nonzero_positions = np.where(np.abs(frame) > 0.01)
    
    if len(nonzero_positions[0]) > 0:
        print(f"\n非零位置:")
        for i, (row, col) in enumerate(zip(nonzero_positions[0], nonzero_positions[1])):
            if i < 10:  # 只显示前10个
                print(f"  [{row},{col}]: {frame[row, col]:.3f}")
    
    return reconFilter

def compare_with_original():
    """与原始函数比较"""
    print("\n" + "=" * 60)
    print("与原始plotReconstruction函数比较")
    print("=" * 60)
    
    # 导入原始函数
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SysIden_AvgMethod_SupportFiles'))
    from SysIden_AvgMethod_SupportFiles.k_functions import plotReconstruction
    
    # 创建测试数据
    filterSize = 3
    numLags = 1
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    filterWeights[1, :, 0] = 1.0  # 水平线
    
    mapWeights = np.ones((2, 2))
    
    # 使用原始函数
    reconFilter_original = plotReconstruction(filterWeights, mapWeights, 1, 1, 6)
    
    # 使用手动函数
    reconFilter_manual, _ = manual_reconstruction(filterWeights, mapWeights, 1, 1, 6)
    
    # 比较结果
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original Filter')
    plt.colorbar()
    
    plt.subplot(1, 3, 2)
    plt.imshow(reconFilter_original[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('plotReconstruction Result')
    plt.colorbar()
    
    plt.subplot(1, 3, 3)
    plt.imshow(reconFilter_manual[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Manual Reconstruction')
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('comparison_reconstruction.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 检查差异
    diff = np.abs(reconFilter_original[:, :, 0] - reconFilter_manual[:, :, 0])
    max_diff = np.max(diff)
    
    print(f"最大差异: {max_diff:.6f}")
    
    if max_diff < 1e-10:
        print("✅ 两种方法结果一致")
    else:
        print("❌ 两种方法结果不同")
        print("差异矩阵:")
        print(diff)

def main():
    """主函数"""
    print("重构逻辑调试工具")
    print("=" * 60)
    
    # 测试简单情况
    test_simple_case()
    
    # 与原始函数比较
    compare_with_original()
    
    print("\n" + "=" * 60)
    print("调试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
