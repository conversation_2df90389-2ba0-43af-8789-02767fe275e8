#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从保存的数值数据中读取并绘制感受野(RF)的脚本
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import scipy.io as sio
from glob import glob
import argparse

# 设置matplotlib中文字体支持
def setup_chinese_font():
    """设置中文字体支持"""
    try:
        # 尝试不同的中文字体
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong']

        for font in chinese_fonts:
            try:
                plt.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                # 测试字体是否可用
                fig, ax = plt.subplots(figsize=(1, 1))
                ax.text(0.5, 0.5, '测试', fontsize=12)
                plt.close(fig)
                print(f"✅ 使用中文字体: {font}")
                return
            except:
                continue

        # 如果所有中文字体都不可用，使用英文
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        print("⚠️  无法找到中文字体，使用英文显示")

    except Exception as e:
        print(f"字体设置失败: {e}")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

# 设置字体
setup_chinese_font()

# 添加支持文件路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SysIden_AvgMethod_SupportFiles'))


def safe_extract_filter_weights(weights_data):
    """安全地提取滤波器权重"""
    try:
        print(f"调试: weights_data类型={type(weights_data)}")
        if hasattr(weights_data, 'shape'):
            print(f"调试: weights_data形状={weights_data.shape}")
        elif hasattr(weights_data, '__len__'):
            print(f"调试: weights_data长度={len(weights_data)}")

        # 方法1: 直接索引
        if hasattr(weights_data, '__getitem__') and len(weights_data) > 0:
            filter_data = weights_data[0]

            # 处理对象数组的情况
            if hasattr(filter_data, 'dtype') and filter_data.dtype == 'object':
                print(f"调试: filter_data是对象数组，尝试提取数值")
                # 尝试从对象数组中提取实际的数值数据
                if hasattr(filter_data, '__getitem__') and len(filter_data) > 0:
                    actual_data = filter_data[0]
                    filter_array = np.asarray(actual_data, dtype=np.float64)
                else:
                    filter_array = np.asarray(filter_data, dtype=np.float64)
            else:
                filter_array = np.asarray(filter_data, dtype=np.float64)

            print(f"调试: 方法1 - filter_array形状={filter_array.shape}, 数据类型={filter_array.dtype}")

            if filter_array.ndim == 4:
                return filter_array[:, :, :, 0]
            elif filter_array.ndim == 3:
                return filter_array
            elif filter_array.ndim == 2:
                return filter_array.reshape(filter_array.shape[0], filter_array.shape[1], 1)
            elif filter_array.ndim == 1:
                # 1维数组，尝试重塑为合理的2D形状
                size = filter_array.shape[0]
                print(f"调试: 处理1维数组，大小={size}")

                if size == 49:  # 7x7
                    return filter_array.reshape(7, 7, 1)
                elif size == 25:  # 5x5
                    return filter_array.reshape(5, 5, 1)
                elif size == 9:  # 3x3
                    return filter_array.reshape(3, 3, 1)
                elif size == 16:  # 4x4
                    return filter_array.reshape(4, 4, 1)
                elif size == 36:  # 6x6
                    return filter_array.reshape(6, 6, 1)
                elif size == 64:  # 8x8
                    return filter_array.reshape(8, 8, 1)
                elif size == 100:  # 10x10
                    return filter_array.reshape(10, 10, 1)
                else:
                    # 尝试找到最接近的平方根
                    sqrt_size = int(np.sqrt(size))
                    if sqrt_size * sqrt_size == size:
                        return filter_array.reshape(sqrt_size, sqrt_size, 1)
                    else:
                        # 对于非平方数，创建一个可视化的1D表示
                        print(f"警告: 非平方数大小={size}，创建1D可视化")
                        # 创建一个1xN的图像用于可视化
                        return filter_array.reshape(1, size, 1)
            else:
                return filter_array

        # 方法2: 对象数组
        if hasattr(weights_data, 'dtype') and weights_data.dtype == 'object':
            filter_data = weights_data.item(0)
            filter_array = np.array(filter_data)
            print(f"调试: 方法2 - filter_array形状={filter_array.shape}")

            if filter_array.ndim == 4:
                return filter_array[:, :, :, 0]
            elif filter_array.ndim == 3:
                return filter_array
            elif filter_array.ndim == 2:
                return filter_array.reshape(filter_array.shape[0], filter_array.shape[1], 1)
            elif filter_array.ndim == 1:
                # 处理1维数组
                size = filter_array.shape[0]
                sqrt_size = int(np.sqrt(size))
                if sqrt_size * sqrt_size == size:
                    return filter_array.reshape(sqrt_size, sqrt_size, 1)
                else:
                    # 创建1D可视化
                    return filter_array.reshape(1, size, 1)
            else:
                return filter_array

        # 方法3: 直接转换
        weights_array = np.array(weights_data)
        print(f"调试: 方法3 - weights_array形状={weights_array.shape}")
        if weights_array.ndim > 0 and len(weights_array) > 0:
            filter_data = weights_array[0]
            filter_array = np.array(filter_data)
            print(f"调试: 方法3 - filter_array形状={filter_array.shape}")

            if filter_array.ndim == 4:
                return filter_array[:, :, :, 0]
            elif filter_array.ndim == 3:
                return filter_array
            elif filter_array.ndim == 2:
                return filter_array.reshape(filter_array.shape[0], filter_array.shape[1], 1)
            elif filter_array.ndim == 1:
                # 处理1维数组
                size = filter_array.shape[0]
                sqrt_size = int(np.sqrt(size))
                if sqrt_size * sqrt_size == size:
                    return filter_array.reshape(sqrt_size, sqrt_size, 1)
                else:
                    # 创建1D可视化
                    return filter_array.reshape(1, size, 1)
            else:
                return filter_array

        return None

    except Exception as e:
        print(f"提取滤波器权重时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def safe_extract_scalar(data, key):
    """安全地从数据中提取标量值"""
    try:
        value = data[key]
        if hasattr(value, 'item'):
            return value.item()
        elif hasattr(value, 'flatten'):
            return value.flatten()[0]
        else:
            return value
    except Exception as e:
        print(f"提取{key}时出错: {e}")
        return None


def find_result_files(base_dir="."):
    """查找所有保存的结果文件"""
    pattern = os.path.join(base_dir, "**/cluster_*_results.mat")
    result_files = glob(pattern, recursive=True)
    return result_files


def load_cluster_results(result_file):
    """加载单个cluster的结果数据"""
    try:
        data = sio.loadmat(result_file)
        return data
    except Exception as e:
        print(f"加载文件 {result_file} 失败: {str(e)}")
        return None


def plot_rf_components(cluster_id, data, save_dir=None):
    """绘制RF的各个组件"""

    if save_dir is None:
        save_dir = f"./rf_plots/cluster_{cluster_id}"
    os.makedirs(save_dir, exist_ok=True)

    # 提取数据
    weights_pass1 = data['weights_pass1']
    weights_pass2 = data['weights_pass2']
    final_rf = data['Final_Rf_Construct']

    # 安全地提取标量值
    vaf = float(safe_extract_scalar(data, 'VAF'))

    gaussian_map = data['gaussian_map']

    print(f"正在绘制 Cluster {cluster_id} 的RF (VAF: {vaf:.2f}%)")

    # 1. 绘制原始滤波器权重
    plt.figure(figsize=(15, 5))

    # 使用安全的权重提取函数
    filterWeights = safe_extract_filter_weights(weights_pass1)

    if filterWeights is None:
        print(f"无法提取 Cluster {cluster_id} 的滤波器权重")
        return save_dir

    print(f"调试: 提取的filterWeights形状={filterWeights.shape}")

    # 确保filterWeights至少是2维的
    if filterWeights.ndim == 1:
        print(f"警告: filterWeights是1维数组，无法绘制")
        return save_dir
    elif filterWeights.ndim == 2:
        # 添加时间维度
        filterWeights = filterWeights.reshape(filterWeights.shape[0], filterWeights.shape[1], 1)

    numFrames = filterWeights.shape[2] if filterWeights.ndim >= 3 else 1

    # 计算对称的颜色范围
    # 确保filterWeights是数值类型
    filterWeights = np.asarray(filterWeights, dtype=np.float64)
    vabs = np.abs(filterWeights)

    if vabs.size > 0:
        vabs_max = float(np.max(vabs))
        if vabs_max == 0:
            vabs_max = 1.0  # 避免除零
    else:
        vabs_max = 1.0  # 默认值
    
    for i in range(numFrames):
        plt.subplot(1, numFrames, i + 1)
        plt.imshow(filterWeights[:, :, i], cmap='RdBu_r', 
                  vmin=-vabs_max, vmax=+vabs_max)
        plt.title(f'Frame {i+1}')
        plt.axis('off')
        plt.colorbar(shrink=0.8)
    
    plt.suptitle(f'Cluster {cluster_id} - Original Filter Weights (VAF: {vaf:.2f}%)', fontsize=14)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'cluster_{cluster_id}_filter_weights.png'), 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    # 2. 绘制重构的感受野
    plt.figure(figsize=(15, 5))
    numFrames_recon = final_rf.shape[2] if final_rf.ndim >= 3 else 1

    # 计算重构RF的对称颜色范围
    vabs_recon = np.abs(final_rf)
    if vabs_recon.size > 0:
        vabs_max_recon = np.max(vabs_recon)
    else:
        vabs_max_recon = 1.0  # 默认值
    
    for i in range(numFrames_recon):
        plt.subplot(1, numFrames_recon, i + 1)
        plt.imshow(final_rf[:, :, i], cmap='RdBu_r', 
                  vmin=-vabs_max_recon, vmax=+vabs_max_recon)
        plt.title(f'Frame {i+1}')
        plt.axis('off')
        plt.colorbar(shrink=0.8)
    
    plt.suptitle(f'Cluster {cluster_id} - Reconstructed Receptive Field (VAF: {vaf:.2f}%)', fontsize=14)
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'cluster_{cluster_id}_reconstructed_rf.png'), 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. 绘制高斯映射
    plt.figure(figsize=(8, 6))
    plt.imshow(gaussian_map, cmap='hot')
    plt.colorbar()
    plt.title(f'Cluster {cluster_id} - Gaussian Map (VAF: {vaf:.2f}%)')
    plt.axis('off')
    plt.savefig(os.path.join(save_dir, f'cluster_{cluster_id}_gaussian_map.png'), 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    # 4. 绘制PReLU Alpha值
    try:
        if isinstance(weights_pass2, (list, tuple)) and len(weights_pass2) > 1:
            plt.figure(figsize=(10, 6))
            preluAlpha = np.array(weights_pass2[1])
            if preluAlpha.size > 0:
                plt.bar(range(len(preluAlpha.flatten())), preluAlpha.flatten())
                plt.title(f'Cluster {cluster_id} - PReLU Alpha Values (VAF: {vaf:.2f}%)')
                plt.xlabel('Filter Index')
                plt.ylabel('Alpha Value')
                plt.grid(True, alpha=0.3)
                plt.savefig(os.path.join(save_dir, f'cluster_{cluster_id}_prelu_alpha.png'),
                            dpi=300, bbox_inches='tight')
                plt.show()
            else:
                print(f"警告: Cluster {cluster_id} 的PReLU Alpha数据为空")
        else:
            print(f"警告: Cluster {cluster_id} 没有PReLU Alpha数据")
    except Exception as e:
        print(f"绘制PReLU Alpha值时出错: {e}")
    
    print(f"已保存 Cluster {cluster_id} 的RF图像到 {save_dir}")
    return save_dir


def plot_multiple_clusters_summary(result_files, max_clusters=None):
    """绘制多个cluster的RF总结图"""
    
    if max_clusters:
        result_files = result_files[:max_clusters]
    
    num_clusters = len(result_files)
    if num_clusters == 0:
        print("没有找到结果文件")
        return
    
    # 创建总结图
    _, axes = plt.subplots(num_clusters, 3, figsize=(15, 5*num_clusters))
    if num_clusters == 1:
        axes = axes.reshape(1, -1)
    
    for i, result_file in enumerate(result_files):
        data = load_cluster_results(result_file)
        if data is None:
            continue
            
        # 安全地提取标量值
        cluster_id = int(safe_extract_scalar(data, 'cluster_id'))
        vaf = float(safe_extract_scalar(data, 'VAF'))
        
        # 原始滤波器权重 (显示第一帧)
        try:
            weights_pass1 = data['weights_pass1']
            filterWeights_full = safe_extract_filter_weights(weights_pass1)

            if filterWeights_full is not None:
                # 取第一帧
                if filterWeights_full.ndim >= 3:
                    filterWeights = filterWeights_full[:, :, 0]
                else:
                    filterWeights = filterWeights_full

                vabs_filter = np.abs(filterWeights)
                if vabs_filter.size > 0:
                    vabs_max = np.max(vabs_filter)
                    if vabs_max > 0:
                        axes[i, 0].imshow(filterWeights, cmap='RdBu_r', vmin=-vabs_max, vmax=vabs_max)
                    else:
                        axes[i, 0].imshow(filterWeights, cmap='RdBu_r')
                else:
                    axes[i, 0].imshow(filterWeights, cmap='RdBu_r')
            else:
                axes[i, 0].text(0.5, 0.5, 'No Data', ha='center', va='center')

            axes[i, 0].set_title(f'Cluster {cluster_id}\nOriginal Filter (Frame 1)')
            axes[i, 0].axis('off')
        except Exception as e:
            print(f"绘制cluster {cluster_id}原始滤波器时出错: {e}")
            axes[i, 0].text(0.5, 0.5, 'Error', ha='center', va='center')
            axes[i, 0].set_title(f'Cluster {cluster_id}\nOriginal Filter (Error)')
            axes[i, 0].axis('off')
        
        # 重构的感受野 (显示第一帧)
        final_rf = data['Final_Rf_Construct'][:, :, 0]
        vabs_recon = np.abs(final_rf)
        if vabs_recon.size > 0:
            vabs_max_recon = np.max(vabs_recon)
            if vabs_max_recon > 0:
                axes[i, 1].imshow(final_rf, cmap='RdBu_r', vmin=-vabs_max_recon, vmax=vabs_max_recon)
            else:
                axes[i, 1].imshow(final_rf, cmap='RdBu_r')
        else:
            axes[i, 1].imshow(final_rf, cmap='RdBu_r')
        axes[i, 1].set_title(f'Reconstructed RF (Frame 1)\nVAF: {vaf:.2f}%')
        axes[i, 1].axis('off')

        # 高斯映射
        gaussian_map = data['gaussian_map']
        axes[i, 2].imshow(gaussian_map, cmap='hot')
        axes[i, 2].set_title(f'Gaussian Map')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('./rf_plots/clusters_summary.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"已保存 {num_clusters} 个cluster的总结图到 ./rf_plots/clusters_summary.png")


def list_available_clusters(base_dir="."):
    """列出所有可用的cluster结果"""
    result_files = find_result_files(base_dir)

    if len(result_files) == 0:
        print("没有找到任何结果文件")
        return []

    clusters_info = []
    print(f"找到 {len(result_files)} 个结果文件:")
    print("-" * 50)

    for file in sorted(result_files):
        data = load_cluster_results(file)
        if data is not None:
            # 安全地提取标量值
            cluster_id = int(safe_extract_scalar(data, 'cluster_id'))
            vaf = float(safe_extract_scalar(data, 'VAF'))

            clusters_info.append((cluster_id, vaf, file))
            print(f"Cluster {cluster_id:3d}: VAF = {vaf:6.2f}% | {file}")

    print("-" * 50)
    return clusters_info


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从保存的数据中绘制感受野')
    parser.add_argument('--cluster_id', type=int, help='指定要绘制的cluster ID')
    parser.add_argument('--base_dir', type=str, default='.', help='搜索结果文件的基础目录')
    parser.add_argument('--max_clusters', type=int, help='总结图中显示的最大cluster数量')
    parser.add_argument('--summary_only', action='store_true', help='只生成总结图')
    parser.add_argument('--list', action='store_true', help='列出所有可用的cluster')

    args = parser.parse_args()
    
    print("=" * 60)
    print("从保存的数据中绘制感受野")
    print("=" * 60)

    # 如果只是列出可用cluster
    if args.list:
        list_available_clusters(args.base_dir)
        return

    # 查找结果文件
    result_files = find_result_files(args.base_dir)

    if len(result_files) == 0:
        print("没有找到任何结果文件，请检查路径")
        print("使用 --list 参数查看可用的cluster")
        return

    # 显示可用的cluster信息
    list_available_clusters(args.base_dir)

    # 按cluster ID排序
    result_files.sort()
    
    if args.cluster_id is not None:
        # 绘制指定的cluster
        target_file = None
        for file in result_files:
            data = load_cluster_results(file)
            if data and int(data['cluster_id']) == args.cluster_id:
                target_file = file
                break
        
        if target_file:
            data = load_cluster_results(target_file)
            plot_rf_components(args.cluster_id, data)
        else:
            print(f"没有找到 cluster {args.cluster_id} 的结果文件")
    
    elif args.summary_only:
        # 只生成总结图
        plot_multiple_clusters_summary(result_files, args.max_clusters)
    
    else:
        # 绘制所有cluster的详细图和总结图
        for result_file in result_files:
            data = load_cluster_results(result_file)
            if data is not None:
                # 安全地提取cluster_id
                cluster_id = int(safe_extract_scalar(data, 'cluster_id'))

                plot_rf_components(cluster_id, data)
        
        # 生成总结图
        plot_multiple_clusters_summary(result_files, args.max_clusters)
    
    print("\n绘制完成！")


if __name__ == "__main__":
    # 如果没有命令行参数，提供交互式使用说明
    if len(sys.argv) == 1:
        print("=" * 60)
        print("感受野(RF)绘制脚本使用说明")
        print("=" * 60)
        print("1. 列出所有可用的cluster:")
        print("   python plot_rf_from_saved_data.py --list")
        print()
        print("2. 绘制所有cluster的详细图和总结图:")
        print("   python plot_rf_from_saved_data.py")
        print()
        print("3. 绘制指定cluster的详细图:")
        print("   python plot_rf_from_saved_data.py --cluster_id 123")
        print()
        print("4. 只生成总结图:")
        print("   python plot_rf_from_saved_data.py --summary_only")
        print()
        print("5. 限制总结图中的cluster数量:")
        print("   python plot_rf_from_saved_data.py --summary_only --max_clusters 10")
        print()
        print("6. 指定搜索目录:")
        print("   python plot_rf_from_saved_data.py --base_dir ./results")
        print("=" * 60)

        # 直接运行默认模式
        print("正在运行默认模式（绘制所有找到的cluster）...")
        print()

    main()
