#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理所有神经元数据的脚本
1. 处理所有的neuron数据，给每一个clusterid都存储一个setresp.mat
2. 保存的mat要用origin_neu_file的名称作为datasetname
3. systemidentificationAVGMETHOD计算每一个神经元的结果并分子文件保存，命名要有前面的clusterid
"""

import os
import sys
import math
import h5py
import pickle
import argparse
import numpy as np
from tqdm import tqdm
from scipy.ndimage import zoom
import scipy.io as sio
import matplotlib.pyplot as plt

# 添加支持文件路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SysIden_AvgMethod_SupportFiles'))
from SysIden_AvgMethod_SupportFiles.k_functions import arrange_stimuli, arrange_responses, conv_output_length, \
    plotGaussMap, plotReconstruction
from SysIden_AvgMethod_SupportFiles.k_model import model_pass1, model_pass2

# 导入配置
from config import get_config

# 获取配置参数
config = get_config()
data_config = config['data']
sysid_config = config['sysid']
split_config = config['data_split']
output_config = config['output']
processing_config = config['processing']
quality_config = config['quality']
cluster_filter_config = config['cluster_filter']

# 提取常用参数
origin_neu_file = data_config['origin_neu_file']
origin_img_file = data_config['origin_img_file']
crop_x1 = sysid_config['crop_x1']
crop_x2 = sysid_config['crop_x2']
crop_y1 = sysid_config['crop_y1']
crop_y2 = sysid_config['crop_y2']
kernel_size = int((crop_x2 - crop_x1) / 30)
num_timelags = sysid_config['num_timelags']
Stride = sysid_config['Stride']
Filter_Size = sysid_config['Filter_Size']
Pool_Size = sysid_config['Pool_Size']


def analyze_image_repetitions(np_img_id):
    """分析图像重复模式"""
    print("正在分析图像重复模式...")

    # 统计每张图像出现的次数
    unique_images, counts = np.unique(np_img_id, return_counts=True)

    print(f"总共有 {len(unique_images)} 张不同的图像")
    print(f"每张图像重复次数统计:")
    print(f"  最小重复次数: {np.min(counts)}")
    print(f"  最大重复次数: {np.max(counts)}")
    print(f"  平均重复次数: {np.mean(counts):.2f}")
    print(f"  重复次数分布: {np.bincount(counts)}")

    # 创建图像索引映射
    img_to_trials = {}
    for trial_idx, img_id in enumerate(np_img_id):
        if img_id not in img_to_trials:
            img_to_trials[img_id] = []
        img_to_trials[img_id].append(trial_idx)

    return unique_images, counts, img_to_trials


def load_neuron_data():
    """加载神经元数据"""
    print("正在加载神经元数据...")

    # 检查文件是否存在
    if not os.path.exists(origin_neu_file):
        print(f"错误：找不到神经元数据文件 {origin_neu_file}")
        print("请确保数据文件在当前目录中")
        return None, None, None, None, None, None, None, None

    with h5py.File(f'./{origin_neu_file}', 'r') as mat_file:
        np_on_time = mat_file['ex']['CondTest']['CondOn'][:, 0]
        np_off_time = mat_file['ex']['CondTest']['CondOff'][:, 0]
        np_img_id = mat_file['ex']['CondTest']['CondIndex'][:, 0] - 1
        np_spiking_cluster_id = mat_file['spike0_kilosort3']['cluster'][0]
        np_spiking_time = mat_file['spike0_kilosort3']['time'][0]
        cluster_pos = mat_file['spike0_kilosort3']['clusterposition'][:]  # n*2 数组
        cluster_id_array = mat_file['spike0_kilosort3']['clusterid'][:][0]
        cluster_good = mat_file['spike0_kilosort3']['clustergood'][:]
        cluster_good = cluster_good.reshape(1, -1)
        # cluster_fr = mat_file['spike0_kilosort3']['clusterfr'][0]
    # 分析图像重复模式
    unique_images, repeat_counts, img_to_trials = analyze_image_repetitions(np_img_id)
    # Step 1: 筛选深度小于600的cluster（使用第二列，索引为1）
    print(f"Cluster位置数据形状: {cluster_pos.shape}")
    print(f"深度范围: {np.min(cluster_pos[1, :]):.1f} - {np.max(cluster_pos[1, :]):.1f}")
    print(f"ClusterGood数据形状: {cluster_good.shape}")
    max_depth_threshold = cluster_filter_config['max_depth_threshold']
    cluster_good_flag = cluster_filter_config['enable_good_filter']
    if cluster_good_flag:
        valid_idx = np.where((cluster_pos[1, :] < max_depth_threshold) & (cluster_good[0, :] == 1))[0]  # 使用第二列（深度）
        print(
            f"深度小于{max_depth_threshold}微米且Clustergood=1的cluster数量: {len(valid_idx)} / {len(cluster_pos[1, :])}")
    else:
        valid_idx = np.where((cluster_pos[1, :] < max_depth_threshold))[0]  # 使用第二列（深度）
        print(f"深度小于{max_depth_threshold}微米的cluster数量: {len(valid_idx)} / {len(cluster_pos[1, :])}")
    # valid_idx = np.where((cluster_pos[1, :] < max_depth_threshold))[0]  # 使用第二列（深度）
    # valid_idx = np.where((cluster_pos[1, :] < max_depth_threshold) & (cluster_good[0, :] == 1))[0]  # 使用第二列（深度）
    # print(f"深度小于{max_depth_threshold}微米且Clustergood=1的cluster数量: {len(valid_idx)} / {len(cluster_pos[1, :])}")

    # Step 2: 构建筛选后的cluster映射字典
    map_cluster_id = {}
    for i, cluster_id in enumerate(cluster_id_array):
        if i in valid_idx:
            map_cluster_id[cluster_id] = i

    print(f"筛选后的有效cluster数量: {len(map_cluster_id)}")

    print(f"找到 {len(map_cluster_id)} 个cluster")
    return np_on_time, np_off_time, np_img_id, np_spiking_cluster_id, np_spiking_time, map_cluster_id, unique_images, img_to_trials


def process_cluster_responses_with_averaging(cluster_id, cluster_index, np_off_time, np_spiking_time,
                                             np_spiking_cluster_id, unique_images, img_to_trials):
    """处理单个cluster的响应数据，计算每张图像的平均反应"""
    if output_config['verbose']:
        print(f"正在处理 cluster {cluster_id} (索引 {cluster_index})...")

    # 筛选当前cluster的spiking时间
    cluster_spiking_time = np_spiking_time[np_spiking_cluster_id == cluster_id]

    # 检查最小spike数量
    if len(cluster_spiking_time) < quality_config['min_spike_count']:
        print(f"⚠️  Cluster {cluster_id} spike数量过少 ({len(cluster_spiking_time)}), 跳过处理")
        return None

    # 首先计算所有trial的响应
    all_trial_responses = []
    left, right = 0, 0
    time_window = sysid_config['time_window']

    desc = f"计算cluster {cluster_id}所有trial响应" if output_config['verbose'] else None
    for i in tqdm(np_off_time, desc=desc, disable=not output_config['verbose']):
        while right < len(cluster_spiking_time) and cluster_spiking_time[right] <= i:
            right += 1
        while left < right and cluster_spiking_time[left] < i - time_window:
            left += 1

        all_trial_responses.append(right - left)

    all_trial_responses = np.array(all_trial_responses)

    # 计算每张图像的平均响应
    print(f"正在计算cluster {cluster_id}每张图像的平均响应...")
    averaged_responses = []

    for img_id in unique_images:
        trial_indices = img_to_trials[img_id]
        img_responses = all_trial_responses[trial_indices]
        avg_response = np.max(img_responses)
        averaged_responses.append(avg_response)

        if output_config['verbose'] and len(unique_images) <= 10:  # 只在图像数量较少时显示详细信息
            print(f"  图像 {img_id}: {len(trial_indices)} 次重复, 平均响应: {avg_response:.2f}")

    averaged_responses = np.array(averaged_responses)

    if output_config['verbose']:
        print(f"Cluster {cluster_id} 平均响应形状: {averaged_responses.shape}")
        print(f"  原始trial数: {len(all_trial_responses)}")
        print(f"  唯一图像数: {len(unique_images)}")
        print(f"  平均响应范围: [{np.min(averaged_responses):.2f}, {np.max(averaged_responses):.2f}]")

    return averaged_responses, all_trial_responses


def split_averaged_responses(averaged_responses, unique_images):
    """将平均响应数据分割为训练、验证和测试集"""
    total_images = len(unique_images)

    # 使用配置中的分割比例，但基于图像数量而不是trial数量
    train_ratio = 0.7  # 70% 用于训练
    val_ratio = 0.1  # 15% 用于验证
    test_ratio = 0.2  # 15% 用于测试

    train_end = int(total_images * train_ratio)
    val_end = int(total_images * (train_ratio + val_ratio))

    # 分割数据
    est_resp = averaged_responses[:train_end]
    reg_resp = averaged_responses[train_end:val_end]
    pred_resp = averaged_responses[val_end:]

    print(f"数据分割: 训练集 {len(est_resp)} 张图像, 验证集 {len(reg_resp)} 张图像, 测试集 {len(pred_resp)} 张图像")

    return est_resp, reg_resp, pred_resp


def save_cluster_averaged_responses(cluster_id, averaged_responses, unique_images, dataset_name):
    """保存单个cluster的平均响应数据"""
    if averaged_responses is None:
        return None

    # 创建输出目录
    output_dir = f"{output_config['data_dir_prefix']}_{cluster_id}{output_config['data_dir_suffix']}"
    os.makedirs(output_dir, exist_ok=True)

    # 分割平均响应数据
    est_resp, reg_resp, pred_resp = split_averaged_responses(averaged_responses, unique_images)

    # 为了兼容现有的SystemIdentification代码，我们需要将1D响应扩展为2D
    # 原始代码期望的格式是 [n_samples, n_repeats]
    est_resp_expanded = np.tile(est_resp.reshape(-1, 1), (1, 5))  # 模拟5次重复
    reg_resp_expanded = np.tile(reg_resp.reshape(-1, 1), (1, 20))  # 模拟20次重复
    pred_resp_expanded = np.tile(pred_resp.reshape(-1, 1), (1, 20))  # 模拟20次重复

    # 保存响应数据
    sio.savemat(os.path.join(output_dir, f'{dataset_name}_estSetResp.mat'), {'est_resp': est_resp_expanded})
    sio.savemat(os.path.join(output_dir, f'{dataset_name}_regSetResp.mat'), {'reg_resp': reg_resp_expanded})
    sio.savemat(os.path.join(output_dir, f'{dataset_name}_predSetResp.mat'), {'pred_resp': pred_resp_expanded})

    # 同时保存原始平均响应数据
    sio.savemat(os.path.join(output_dir, f'{dataset_name}_averaged_responses.mat'), {
        'averaged_responses': averaged_responses,
        'unique_images': unique_images,
        'est_resp': est_resp,
        'reg_resp': reg_resp,
        'pred_resp': pred_resp
    })

    if output_config['verbose']:
        print(f"已保存cluster {cluster_id}的平均响应数据到 {output_dir}")
    return output_dir


def load_stimuli_data_averaged(unique_images, img_to_trials):
    """加载刺激数据，基于唯一图像而不是所有trials"""
    print("正在加载刺激数据（基于唯一图像）...")

    if not os.path.exists(origin_img_file):
        print(f"错误：找不到刺激数据文件 {origin_img_file}")
        return None

    # 检查是否已经存在处理好的平均刺激数据
    stimuli_file = 'DataSets_ZHJ2024_30_averaged.mat'
    # if os.path.exists(stimuli_file):
    #     print(f"使用已存在的平均刺激数据文件: {stimuli_file}")
    #     return sio.loadmat(stimuli_file)

    # 处理原始刺激数据
    print("处理原始刺激数据...")
    with h5py.File(f'./{origin_img_file}', 'r') as mat_file:
        all_images = np.array([
            zoom(mat_file[i][:, :], 30 / 64, order=3)
            for i in tqdm(mat_file['imgset'], desc="加载所有图像")
        ])

    # 只保留唯一图像（每张图像只取一次）
    unique_stimuli = all_images[unique_images]
    unique_stimuli = np.transpose(unique_stimuli, [1, 2, 0])

    print(f"原始图像数量: {len(all_images)}")
    print(f"唯一图像数量: {len(unique_images)}")
    print(f"刺激数据形状: {unique_stimuli.shape}")

    # 基于图像数量分割数据
    total_images = len(unique_images)
    train_end = int(total_images * 0.7)
    val_end = int(total_images * 0.2)

    data = {
        'Training_stimuli': unique_stimuli[:, :, :16800],
        'Validation_stimuli': unique_stimuli[:, :, 16800:19200],
        'Testing_stimuli': unique_stimuli[:, :, 19200:]
    }

    print(f"训练刺激: {data['Training_stimuli'].shape}")
    print(f"验证刺激: {data['Validation_stimuli'].shape}")
    print(f"测试刺激: {data['Testing_stimuli'].shape}")

    sio.savemat(stimuli_file, data)
    print(f"已保存平均刺激数据到 {stimuli_file}")

    return data


def load_stimuli_data():
    """加载刺激数据（原始版本，保持兼容性）"""
    print("正在加载刺激数据...")

    if not os.path.exists(origin_img_file):
        print(f"错误：找不到刺激数据文件 {origin_img_file}")
        return None

    # 检查是否已经存在处理好的刺激数据
    stimuli_file = 'DataSets_ZHJ2024_30.mat'
    if os.path.exists(stimuli_file):
        print(f"使用已存在的刺激数据文件: {stimuli_file}")
        return sio.loadmat(stimuli_file)

    # 如果不存在，则处理原始刺激数据
    print("处理原始刺激数据...")
    with h5py.File(f'./{origin_img_file}', 'r') as mat_file:
        np_img = np.array([
            zoom(mat_file[i][:, :], 30 / 64, order=3)
            for i in tqdm(mat_file['imgset'])
        ])

    # 加载图像索引
    with h5py.File(f'./{origin_neu_file}', 'r') as mat_file:
        np_img_id = mat_file['ex']['CondTest']['CondIndex'][:, 0] - 1

    np_img = np_img[np_img_id]
    np_img = np.transpose(np_img, [1, 2, 0])

    data = {
        'Training_stimuli': np_img[:, :, :16800],
        'Validation_stimuli': np_img[:, :, 16800:19200],
        'Testing_stimuli': np_img[:, :, 19200:]
    }

    sio.savemat(stimuli_file, data)
    print(f"已保存刺激数据到 {stimuli_file}")

    return data


def run_system_identification(cluster_id, dataset_path, dataset_name, stimuli_dataset):
    """运行SystemIdentification算法计算RF"""
    print(f"正在为cluster {cluster_id}运行SystemIdentification...")

    try:
        # 导入必要的库
        import scipy.io as sio
        import numpy as np
        from tensorflow import keras
        import matplotlib.pyplot as plt
        from tensorflow.keras.callbacks import ModelCheckpoint
        import tensorflow as tf
        import scipy
        import sys
        sys.path.insert(0,
                        'D:\\code\\CNNwithPReLU_RFestimateV1\\Real V1 Neuron\\SysIden_AvgMethod_SupportFiles')  # path to the support files folder
        from SysIden_AvgMethod_SupportFiles.k_functions import arrange_stimuli, arrange_responses, conv_output_length, \
            plotGaussMap, plotReconstruction
        from SysIden_AvgMethod_SupportFiles.k_model import model_pass1, model_pass2

        # 准备刺激数据
        estSet, regSet, predSet, imSize = arrange_stimuli(stimuli_dataset, crop_y1, crop_y2, crop_x1, crop_x2,
                                                          kernel_size, num_timelags)

        # 准备响应数据 - 使用自定义函数处理单个cluster
        y_est, y_reg, y_pred, num_neurons = arrange_responses(dataset_path, dataset_name)

        # 计算输入形状
        Input_Shape = estSet.shape[1:]
        numRows = Input_Shape[0]
        numCols = Input_Shape[1]
        assert numRows == numCols
        numFrames = Input_Shape[2]

        # 计算卷积层输出大小
        convImageSize = conv_output_length(numRows, Filter_Size, 'valid', Stride)
        # 高斯映射层的输入大小应该等于卷积层的输出大小
        downsampImageSize = conv_output_length(convImageSize, Pool_Size, 'valid',
                                               Pool_Size)  # Input to Gaussian Map Layer

        # 第一阶段训练
        model1 = model_pass1(Input_Shape, Filter_Size, Stride, Pool_Size, downsampImageSize)
        model1.summary()
        optimizerFunction = keras.optimizers.Adam(learning_rate=0.001)
        model1.compile(loss='mse', optimizer=optimizerFunction)
        earlyStop = keras.callbacks.EarlyStopping(monitor='val_loss', patience=20, verbose=0, mode='auto')
        mc = ModelCheckpoint('best_model_pass1.keras', monitor='val_loss', mode='min', verbose=1)
        history1 = model1.fit(estSet, y_est, validation_data=(regSet, y_reg), epochs=500,
                              batch_size=750, callbacks=[earlyStop, mc], verbose=1)

        weights = model1.get_weights()

        # 第二阶段训练
        print(f"Cluster {cluster_id}: 开始第二阶段训练...")
        Initial_Filter_Weights = [weights[0], weights[1]]  # Receptive Field Estimates from Pass 1
        Initial_exp = np.asarray([1])  # Intialize Power Law Exponet to 1

        model2 = model_pass2(Input_Shape, Filter_Size, Stride, Pool_Size, downsampImageSize, Initial_Filter_Weights,
                             Initial_exp)
        model2.summary()
        optimizerFunction = keras.optimizers.Adam(learning_rate=0.001)
        model2.compile(loss='mse', optimizer=optimizerFunction)
        earlyStop = keras.callbacks.EarlyStopping(monitor='val_loss', patience=20, verbose=0, mode='auto')
        mc = ModelCheckpoint('best_model_pass2.keras', monitor='val_loss', mode='min', verbose=1)
        history2 = model2.fit(estSet, y_est, validation_data=(regSet, y_reg), epochs=500,
                              batch_size=750, callbacks=[earlyStop, mc], verbose=1)

        # 计算VAF
        predicted_test_response = model2.predict(predSet)
        predicted_test_response = predicted_test_response.reshape(-1)
        respTest = y_pred.reshape(-1)
        R = np.corrcoef(predicted_test_response, respTest)
        diag = R[0, 1]
        VAF_test = diag * diag * 100

        print(f"Cluster {cluster_id}: VAF = {VAF_test:.2f}%")
        weights2 = model2.get_weights()
        # 生成结果图像和数据
        results = generate_results(cluster_id, weights, weights2, downsampImageSize, imSize,
                                   predicted_test_response, VAF_test, history1, history2)

        return results

    except Exception as e:
        print(f"Cluster {cluster_id}处理失败: {str(e)}")
        return None


def generate_results(cluster_id, weights, weights2, downsampImageSize, imSize,
                     predicted_test_response, VAF_test, history1, history2):
    """生成并保存结果"""

    # 创建结果目录
    # results_dir = config['output']['results_dir_prefix']
    results_dir = f"./PLCATD001_Ephys_DA07_Image_0/results/cluster_{cluster_id}_results"
    os.makedirs(results_dir, exist_ok=True)

    # 1. 学习曲线
    plt.figure()
    plt.subplot(1, 2, 1)
    plt.plot(history1.history['loss'])
    plt.plot(history1.history['val_loss'])
    plt.title(f'Cluster {cluster_id} - Pass 1 Learning Curve')
    plt.ylabel('loss')
    plt.xlabel('epoch')
    plt.legend(['train', 'validation'], loc='upper right')
    plt.grid()

    plt.subplot(1, 2, 2)
    plt.plot(history2.history['loss'])
    plt.plot(history2.history['val_loss'])
    plt.title(f'Cluster {cluster_id} - Pass 2 Learning Curve')
    plt.ylabel('loss')
    plt.xlabel('epoch')
    plt.legend(['train', 'validation'], loc='upper right')
    plt.grid()

    plt.tight_layout()
    plt.savefig(os.path.join(results_dir, f'cluster_{cluster_id}_learning_curve.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. PReLU Alpha值
    plt.figure()
    preluAlpha = weights2[1]
    plt.bar(range(len(preluAlpha)), preluAlpha.flatten())
    plt.title(f'Cluster {cluster_id} - PReLU Alpha Values')
    plt.xlabel('Filter Index')
    plt.ylabel('Alpha Value')
    plt.savefig(os.path.join(results_dir, f'cluster_{cluster_id}_prelu_alpha.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # # 3. PReLU
    # plt.figure()
    # alpha1 = np.squeeze(weights2[2])
    # x = np.arange(-100,101)
    # y = np.arange(-100,101)
    # y[y<=0] = alpha1*y[y<=0]
    # plt.plot(x,y)
    # plt.title('PReLU, alpha = {}'.format(np.round(alpha1,2)))
    # plt.savefig(os.path.join(results_dir, f'cluster_{cluster_id}_PReLU_Activation.png'), dpi=300, bbox_inches='tight')
    # plt.close()

    # 4. 高斯映射
    plt.figure()
    mapMean = weights2[3]
    mapSigma = weights2[4]
    mapVals = plotGaussMap(mapMean, mapSigma, downsampImageSize)
    plt.title(f'Cluster {cluster_id} - Gaussian Map')
    plt.savefig(os.path.join(results_dir, f'cluster_{cluster_id}_gaussian_map.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 5. 感受野滤波器权重
    plt.figure()
    filterWeights = weights[0][:, :, :, 0]
    numFrames = filterWeights.shape[2]
    vmin = np.min(filterWeights)
    vmax = np.max(filterWeights)
    vabs = np.abs(filterWeights)
    vabs_max = np.max(vabs)
    for i in range(numFrames):
        plt.subplot(1, numFrames, i + 1)
        plt.imshow(filterWeights[:, :, i], vmin=-vabs_max, vmax=+vabs_max)
    plt.suptitle(f'Cluster {cluster_id} - Receptive Field Filter Weights')
    plt.savefig(os.path.join(results_dir, f'cluster_{cluster_id}_rf_filter_weights.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 6. 重构的感受野滤波器
    plt.figure()
    reconFilter = plotReconstruction(filterWeights, mapVals, Stride, Pool_Size, imSize[0])
    plt.suptitle(f'Cluster {cluster_id} - Reconstructed Linear Filter')
    plt.savefig(os.path.join(results_dir, f'cluster_{cluster_id}_reconstructed_filter.png'), dpi=300,
                bbox_inches='tight')
    plt.close()

    # 保存结果到.mat文件
    results_data = {
        'cluster_id': cluster_id,
        'weights_pass1': weights,
        'weights_pass2': weights2,
        'Final_Rf_Construct': reconFilter,
        'VAF': VAF_test,
        'Predicted_response': predicted_test_response,
        'gaussian_map': mapVals
    }

    sio.savemat(os.path.join(results_dir, f'cluster_{cluster_id}_results.mat'), results_data)

    print(f"已保存cluster {cluster_id}的结果到 {results_dir}")

    return results_data


def main():
    """主函数"""
    print("=" * 60)
    print("开始处理所有神经元数据")
    print("=" * 60)

    # 1. 加载神经元数据
    neuron_data = load_neuron_data()
    if neuron_data[0] is None:
        print("无法加载神经元数据，程序退出")
        return

    np_on_time, np_off_time, np_img_id, np_spiking_cluster_id, np_spiking_time, map_cluster_id, unique_images, img_to_trials = neuron_data

    # 2. 加载刺激数据（基于唯一图像）
    stimuli_dataset = load_stimuli_data_averaged(unique_images, img_to_trials)
    if stimuli_dataset is None:
        print("无法加载刺激数据，程序退出")
        return

    # 3. 获取dataset名称（从origin_neu_file中提取）
    dataset_name = os.path.splitext(origin_neu_file)[0]  # 去掉.mat扩展名
    print(f"使用dataset名称: {dataset_name}")

    # 4. 处理每个cluster
    all_results = {}
    successful_clusters = []
    failed_clusters = []

    print(f"\n开始处理 {len(map_cluster_id)} 个clusters...")

    for cluster_id, cluster_index in tqdm(map_cluster_id.items(), desc="处理clusters"):
        try:
            print(f"\n{'=' * 40}")
            print(f"处理 Cluster {cluster_id} ({cluster_index + 1}/{len(map_cluster_id)})")
            print(f"{'=' * 40}")

            # 处理响应数据，计算每张图像的平均反应
            result = process_cluster_responses_with_averaging(cluster_id, cluster_index, np_off_time,
                                                              np_spiking_time, np_spiking_cluster_id,
                                                              unique_images, img_to_trials)

            if result is None:
                failed_clusters.append(cluster_id)
                print(f"✗ Cluster {cluster_id} 处理失败：响应数据为空")
                continue

            averaged_responses, all_trial_responses = result

            # 保存平均响应数据
            output_dir = save_cluster_averaged_responses(cluster_id, averaged_responses, unique_images, dataset_name)

            # 运行SystemIdentification
            results = run_system_identification(cluster_id, output_dir, dataset_name, stimuli_dataset)

            if results is not None:
                all_results[cluster_id] = results
                successful_clusters.append(cluster_id)
                print(f"✓ Cluster {cluster_id} 处理成功")
            else:
                failed_clusters.append(cluster_id)
                print(f"✗ Cluster {cluster_id} 处理失败")

        except Exception as e:
            failed_clusters.append(cluster_id)
            print(f"✗ Cluster {cluster_id} 处理失败: {str(e)}")
            continue

    # 5. 生成总结报告
    print(f"\n{'=' * 60}")
    print("处理完成总结")
    print(f"{'=' * 60}")
    print(f"总cluster数量: {len(map_cluster_id)}")
    print(f"成功处理: {len(successful_clusters)}")
    print(f"处理失败: {len(failed_clusters)}")

    if successful_clusters:
        print(f"\n成功处理的clusters: {successful_clusters}")

    if failed_clusters:
        print(f"\n失败的clusters: {failed_clusters}")

    # 保存总结结果
    summary_data = {
        'total_clusters': len(map_cluster_id),
        'successful_clusters': successful_clusters,
        'failed_clusters': failed_clusters,
        'dataset_name': dataset_name,
        'processing_parameters': {
            'crop_x1': crop_x1, 'crop_x2': crop_x2,
            'crop_y1': crop_y1, 'crop_y2': crop_y2,
            'num_timelags': num_timelags,
            'Stride': Stride,
            'Filter_Size': Filter_Size,
            'Pool_Size': Pool_Size
        }
    }

    sio.savemat('processing_summary.mat', summary_data)
    print(f"\n已保存处理总结到 processing_summary.mat")

    print(f"\n{'=' * 60}")
    print("所有处理完成！")
    print(f"{'=' * 60}")


if __name__ == "__main__":
    main()
