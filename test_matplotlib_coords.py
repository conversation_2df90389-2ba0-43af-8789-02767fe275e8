#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试matplotlib的坐标系约定
"""

import numpy as np
import matplotlib.pyplot as plt

def test_matplotlib_coordinates():
    """测试matplotlib的坐标系"""
    print("=" * 50)
    print("测试matplotlib坐标系约定")
    print("=" * 50)
    
    # 创建一个5x5的测试图像
    img = np.zeros((5, 5))
    
    # 在特定位置设置值来测试坐标系
    img[0, 0] = 1.0  # 左上角
    img[0, 4] = 2.0  # 右上角
    img[4, 0] = 3.0  # 左下角
    img[4, 4] = 4.0  # 右下角
    img[2, 2] = 5.0  # 中心
    
    # 添加一个水平线（第1行）
    img[1, 1:4] = 0.5
    
    # 添加一个垂直线（第1列）
    img[1:4, 1] = 0.7
    
    print("数组内容:")
    print("img[0,0]=1 (应该在左上角)")
    print("img[0,4]=2 (应该在右上角)")
    print("img[4,0]=3 (应该在左下角)")
    print("img[4,4]=4 (应该在右下角)")
    print("img[2,2]=5 (应该在中心)")
    print("img[1,1:4]=0.5 (应该是水平线)")
    print("img[1:4,1]=0.7 (应该是垂直线)")
    print()
    
    print("实际数组:")
    print(img)
    print()
    
    # 显示图像
    plt.figure(figsize=(12, 4))
    
    # 默认显示
    plt.subplot(1, 3, 1)
    plt.imshow(img, cmap='viridis', interpolation='nearest')
    plt.title('Default imshow')
    plt.colorbar()
    
    # 添加坐标标注
    for i in range(5):
        for j in range(5):
            if img[i, j] > 0:
                plt.text(j, i, f'({i},{j})', ha='center', va='center', 
                        color='white', fontsize=8)
    
    # 使用origin='upper'（默认）
    plt.subplot(1, 3, 2)
    plt.imshow(img, cmap='viridis', interpolation='nearest', origin='upper')
    plt.title("origin='upper' (default)")
    plt.colorbar()
    
    # 使用origin='lower'
    plt.subplot(1, 3, 3)
    plt.imshow(img, cmap='viridis', interpolation='nearest', origin='lower')
    plt.title("origin='lower'")
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('matplotlib_coords_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("matplotlib坐标系说明:")
    print("- 默认origin='upper': (0,0)在左上角")
    print("- img[row, col]中，row对应y轴（垂直），col对应x轴（水平）")
    print("- imshow显示时，第一个维度（行）对应垂直方向")
    print("- 第二个维度（列）对应水平方向")

def test_array_indexing():
    """测试数组索引与显示的对应关系"""
    print("\n" + "=" * 50)
    print("测试数组索引与显示的对应关系")
    print("=" * 50)
    
    # 创建一个简单的L形
    img = np.zeros((6, 6))
    
    # L形：水平部分在第1行，垂直部分在第1列
    img[1, 1:5] = 1.0  # 水平线：行1，列1-4
    img[1:5, 1] = 1.0  # 垂直线：行1-4，列1
    
    print("创建L形:")
    print("img[1, 1:5] = 1.0  # 水平线")
    print("img[1:5, 1] = 1.0  # 垂直线")
    print()
    print("数组内容:")
    print(img)
    print()
    
    plt.figure(figsize=(8, 4))
    
    plt.subplot(1, 2, 1)
    plt.imshow(img, cmap='RdBu_r', interpolation='nearest')
    plt.title('L-shape Test')
    plt.colorbar()
    
    # 添加网格
    for i in range(7):
        plt.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
        plt.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
    
    # 测试转置
    plt.subplot(1, 2, 2)
    plt.imshow(img.T, cmap='RdBu_r', interpolation='nearest')
    plt.title('Transposed L-shape')
    plt.colorbar()
    
    # 添加网格
    for i in range(7):
        plt.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
        plt.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('array_indexing_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("如果L形在左图中正确显示（水平线在上，垂直线在左），")
    print("说明我们的坐标系理解是正确的。")
    print("如果右图（转置）中L形旋转了90度，说明转置确实改变了方向。")

def main():
    """主函数"""
    test_matplotlib_coordinates()
    test_array_indexing()
    
    print("\n" + "=" * 50)
    print("坐标系测试完成！")
    print("现在可以运行 test_coordinate_system.py 来测试RF重构")
    print("=" * 50)

if __name__ == "__main__":
    main()
