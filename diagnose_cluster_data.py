#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断cluster数据结构
"""

import numpy as np
import scipy.io as sio
from glob import glob

def analyze_cluster_data():
    """分析cluster数据的详细结构"""
    # 找到结果文件
    pattern = "**/cluster_*_results.mat"
    result_files = glob(pattern, recursive=True)
    
    if len(result_files) == 0:
        print("没有找到结果文件")
        return
    
    file_path = result_files[0]
    print(f"分析文件: {file_path}")
    print("=" * 60)
    
    try:
        data = sio.loadmat(file_path)
        
        # 显示所有键
        print("文件中的所有键:")
        for key in sorted(data.keys()):
            if not key.startswith('__'):
                value = data[key]
                print(f"  {key}: 类型={type(value)}, 形状={getattr(value, 'shape', 'N/A')}")
        print()
        
        # 详细分析weights_pass1
        if 'weights_pass1' in data:
            weights_pass1 = data['weights_pass1']
            print("weights_pass1 详细分析:")
            print(f"  类型: {type(weights_pass1)}")
            print(f"  形状: {weights_pass1.shape}")
            print(f"  数据类型: {weights_pass1.dtype}")
            
            if weights_pass1.dtype == 'object':
                print("  这是一个对象数组，包含:")
                for i in range(min(len(weights_pass1), 5)):  # 只显示前5个
                    item = weights_pass1[i]
                    if hasattr(item, 'shape'):
                        print(f"    [{i}]: 形状={item.shape}, 类型={type(item)}")
                    else:
                        print(f"    [{i}]: 类型={type(item)}, 值={item}")
            else:
                print(f"  值范围: [{np.min(weights_pass1):.6f}, {np.max(weights_pass1):.6f}]")
                if weights_pass1.size <= 20:
                    print(f"  所有值: {weights_pass1.flatten()}")
        
        # 详细分析weights_pass2
        if 'weights_pass2' in data:
            weights_pass2 = data['weights_pass2']
            print("\nweights_pass2 详细分析:")
            print(f"  类型: {type(weights_pass2)}")
            print(f"  形状: {weights_pass2.shape}")
            print(f"  数据类型: {weights_pass2.dtype}")
            
            if weights_pass2.dtype == 'object':
                print("  这是一个对象数组，包含:")
                for i in range(min(len(weights_pass2), 5)):
                    item = weights_pass2[i]
                    if hasattr(item, 'shape'):
                        print(f"    [{i}]: 形状={item.shape}, 类型={type(item)}")
                    else:
                        print(f"    [{i}]: 类型={type(item)}")
        
        # 分析Final_Rf_Construct
        if 'Final_Rf_Construct' in data:
            final_rf = data['Final_Rf_Construct']
            print(f"\nFinal_Rf_Construct:")
            print(f"  形状: {final_rf.shape}")
            print(f"  数据类型: {final_rf.dtype}")
            print(f"  值范围: [{np.min(final_rf):.6f}, {np.max(final_rf):.6f}]")
            
            # 检查是否有有效数据
            nonzero_count = np.count_nonzero(np.abs(final_rf) > 1e-10)
            print(f"  非零元素数量: {nonzero_count} / {final_rf.size}")
        
        # 分析VAF
        if 'VAF' in data:
            vaf = data['VAF']
            print(f"\nVAF:")
            print(f"  形状: {vaf.shape}")
            print(f"  值: {vaf}")
            
        # 分析gaussian_map
        if 'gaussian_map' in data:
            gaussian_map = data['gaussian_map']
            print(f"\ngaussian_map:")
            print(f"  形状: {gaussian_map.shape}")
            print(f"  数据类型: {gaussian_map.dtype}")
            print(f"  值范围: [{np.min(gaussian_map):.6f}, {np.max(gaussian_map):.6f}]")
        
        # 检查这个cluster是否训练成功
        print(f"\n训练质量评估:")
        vaf_value = float(data['VAF'].item() if hasattr(data['VAF'], 'item') else data['VAF'])
        print(f"  VAF: {vaf_value:.4f}%")
        
        if vaf_value < 1.0:
            print("  ⚠️  VAF非常低，这个cluster可能训练失败")
            print("  建议：")
            print("    1. 检查这个cluster的spike数据质量")
            print("    2. 尝试其他VAF更高的cluster")
            print("    3. 检查训练参数设置")
        elif vaf_value < 5.0:
            print("  ⚠️  VAF较低，训练效果不佳")
        else:
            print("  ✅ VAF正常")
            
    except Exception as e:
        print(f"分析失败: {e}")
        import traceback
        traceback.print_exc()

def suggest_better_clusters():
    """建议更好的cluster"""
    print("\n" + "=" * 60)
    print("寻找更好的cluster")
    print("=" * 60)
    
    pattern = "**/cluster_*_results.mat"
    result_files = glob(pattern, recursive=True)
    
    if len(result_files) <= 1:
        print("只找到1个cluster，无法比较")
        return
    
    cluster_info = []
    
    for file_path in result_files:
        try:
            data = sio.loadmat(file_path)
            cluster_id = int(data['cluster_id'].item() if hasattr(data['cluster_id'], 'item') else data['cluster_id'])
            vaf = float(data['VAF'].item() if hasattr(data['VAF'], 'item') else data['VAF'])
            cluster_info.append((cluster_id, vaf, file_path))
        except:
            continue
    
    # 按VAF排序
    cluster_info.sort(key=lambda x: x[1], reverse=True)
    
    print(f"找到 {len(cluster_info)} 个cluster，按VAF排序:")
    for i, (cluster_id, vaf, file_path) in enumerate(cluster_info[:10]):  # 显示前10个
        status = "✅" if vaf > 5.0 else "⚠️" if vaf > 1.0 else "❌"
        print(f"  {status} Cluster {cluster_id}: VAF = {vaf:.2f}%")
    
    if len(cluster_info) > 0:
        best_cluster = cluster_info[0]
        print(f"\n推荐使用 Cluster {best_cluster[0]} (VAF: {best_cluster[1]:.2f}%)")
        print(f"命令: python plot_rf_from_saved_data.py --cluster_id {best_cluster[0]}")

def main():
    """主函数"""
    print("Cluster数据诊断工具")
    print("=" * 60)
    
    analyze_cluster_data()
    suggest_better_clusters()

if __name__ == "__main__":
    main()
