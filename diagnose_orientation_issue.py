#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断方向问题
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加支持文件路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SysIden_AvgMethod_SupportFiles'))
from SysIden_AvgMethod_SupportFiles.k_functions import plotReconstruction

def analyze_reconstruction_step_by_step():
    """逐步分析重构过程"""
    print("=" * 60)
    print("逐步分析重构过程")
    print("=" * 60)
    
    # 创建简单的水平线
    filterSize = 3
    numLags = 1
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    filterWeights[1, :, 0] = 1.0  # 中间行为1（水平线）
    
    print("步骤1: 原始滤波器（水平线）")
    print(filterWeights[:, :, 0])
    print("期望：中间行应该是水平线")
    
    # 简单的映射权重
    mapSize = 2
    mapWeights = np.ones((mapSize, mapSize))
    
    print(f"\n步骤2: 映射权重 ({mapSize}x{mapSize})")
    print(mapWeights)
    
    # 手动重构第一步：创建unPoolFilter
    stride = 1
    poolSize = 1
    unPoolSize = mapSize * poolSize * stride
    unPoolFilter = np.zeros((unPoolSize, unPoolSize))
    
    print(f"\n步骤3: 创建unPoolFilter ({unPoolSize}x{unPoolSize})")
    
    for map_y_idx in range(mapSize):
        for map_x_idx in range(mapSize):
            y_start = map_y_idx * poolSize * stride
            y_end = (map_y_idx + 1) * poolSize * stride
            x_start = map_x_idx * poolSize * stride
            x_end = (map_x_idx + 1) * poolSize * stride
            
            weight_value = mapWeights[map_y_idx, map_x_idx]
            unPoolFilter[y_start:y_end, x_start:x_end] += weight_value
            
            print(f"  map[{map_y_idx},{map_x_idx}]={weight_value} -> unPool[{y_start}:{y_end},{x_start}:{x_end}]")
    
    print("unPoolFilter结果:")
    print(unPoolFilter)
    
    # 手动重构第二步
    fullSize = 6
    reconFilter = np.zeros((fullSize, fullSize, numLags))
    
    print(f"\n步骤4: 重构到 {fullSize}x{fullSize}")
    
    for lag in range(numLags):
        for y_idx in range(unPoolSize):
            for x_idx in range(unPoolSize):
                if unPoolFilter[y_idx, x_idx] != 0:
                    # 计算放置位置
                    y_start = y_idx
                    y_end = min(y_idx + filterSize, fullSize)
                    x_start = x_idx
                    x_end = min(x_idx + filterSize, fullSize)
                    
                    # 计算滤波器片段大小
                    filter_y_size = y_end - y_start
                    filter_x_size = x_end - x_start
                    
                    if filter_y_size > 0 and filter_x_size > 0:
                        weight = unPoolFilter[y_idx, x_idx]
                        filter_patch = filterWeights[:filter_y_size, :filter_x_size, lag]
                        
                        print(f"  位置[{y_idx},{x_idx}] 权重={weight} -> recon[{y_start}:{y_end},{x_start}:{x_end}]")
                        print(f"    滤波器片段形状: {filter_patch.shape}")
                        print(f"    滤波器片段:")
                        print(f"    {filter_patch}")
                        
                        reconFilter[y_start:y_end, x_start:x_end, lag] += weight * filter_patch
    
    print(f"\n最终重构结果:")
    print(reconFilter[:, :, 0])
    
    # 可视化
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original (Horizontal Line)')
    plt.colorbar()
    # 添加网格
    for i in range(filterSize + 1):
        plt.axhline(i - 0.5, color='gray', linewidth=0.5)
        plt.axvline(i - 0.5, color='gray', linewidth=0.5)
    
    plt.subplot(1, 3, 2)
    plt.imshow(unPoolFilter, cmap='hot', interpolation='nearest')
    plt.title('UnPool Filter')
    plt.colorbar()
    
    plt.subplot(1, 3, 3)
    plt.imshow(reconFilter[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed')
    plt.colorbar()
    # 添加网格
    for i in range(fullSize + 1):
        plt.axhline(i - 0.5, color='gray', linewidth=0.5)
        plt.axvline(i - 0.5, color='gray', linewidth=0.5)
    
    plt.tight_layout()
    plt.savefig('step_by_step_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return reconFilter

def test_different_orientations():
    """测试不同方向的线条"""
    print("\n" + "=" * 60)
    print("测试不同方向的线条")
    print("=" * 60)
    
    # 创建三种不同的线条
    filterSize = 3
    numLags = 1
    
    # 水平线
    h_filter = np.zeros((filterSize, filterSize, numLags))
    h_filter[1, :, 0] = 1.0
    
    # 垂直线
    v_filter = np.zeros((filterSize, filterSize, numLags))
    v_filter[:, 1, 0] = 1.0
    
    # 对角线
    d_filter = np.zeros((filterSize, filterSize, numLags))
    for i in range(filterSize):
        d_filter[i, i, 0] = 1.0
    
    filters = [h_filter, v_filter, d_filter]
    names = ['Horizontal', 'Vertical', 'Diagonal']
    
    plt.figure(figsize=(18, 12))
    
    mapWeights = np.ones((2, 2))
    
    for i, (filt, name) in enumerate(zip(filters, names)):
        # 原始滤波器
        plt.subplot(3, 3, i*3 + 1)
        plt.imshow(filt[:, :, 0], cmap='RdBu_r', interpolation='nearest')
        plt.title(f'Original {name}')
        plt.colorbar()
        
        # 重构
        recon = plotReconstruction(filt, mapWeights, 1, 1, 6)
        
        plt.subplot(3, 3, i*3 + 2)
        plt.imshow(recon[:, :, 0], cmap='RdBu_r', interpolation='nearest')
        plt.title(f'Reconstructed {name}')
        plt.colorbar()
        
        # 能量分析
        frame = recon[:, :, 0]
        h_energy = np.sum(np.abs(np.diff(frame, axis=1)))
        v_energy = np.sum(np.abs(np.diff(frame, axis=0)))
        
        plt.subplot(3, 3, i*3 + 3)
        plt.bar(['Horizontal', 'Vertical'], [h_energy, v_energy])
        plt.title(f'{name} Energy')
        plt.ylabel('Energy')
        
        print(f"{name} 线条:")
        print(f"  水平能量: {h_energy:.2f}")
        print(f"  垂直能量: {v_energy:.2f}")
        
        if name == 'Horizontal':
            if h_energy > v_energy:
                print("  ✅ 水平线保持水平")
            else:
                print("  ❌ 水平线变成垂直")
        elif name == 'Vertical':
            if v_energy > h_energy:
                print("  ✅ 垂直线保持垂直")
            else:
                print("  ❌ 垂直线变成水平")
    
    plt.tight_layout()
    plt.savefig('orientation_test.png', dpi=150, bbox_inches='tight')
    plt.show()

def test_minimal_case():
    """测试最小情况"""
    print("\n" + "=" * 60)
    print("测试最小情况")
    print("=" * 60)
    
    # 最简单的情况：1x1映射，2x2滤波器
    filterWeights = np.array([[[0, 1],
                              [0, 0]]]).transpose(1, 2, 0)  # 2x2x1
    
    mapWeights = np.array([[1.0]])  # 1x1
    
    print("原始滤波器 (2x2):")
    print(filterWeights[:, :, 0])
    print("期望：右上角有一个点")
    
    print("\n映射权重 (1x1):")
    print(mapWeights)
    
    # 重构
    recon = plotReconstruction(filterWeights, mapWeights, 1, 1, 4)
    
    print(f"\n重构结果:")
    print(recon[:, :, 0])
    
    plt.figure(figsize=(10, 4))
    
    plt.subplot(1, 2, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original 2x2')
    plt.colorbar()
    
    plt.subplot(1, 2, 2)
    plt.imshow(recon[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed 4x4')
    plt.colorbar()
    
    plt.tight_layout()
    plt.savefig('minimal_test.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("方向问题诊断工具")
    print("=" * 60)
    
    # 逐步分析
    analyze_reconstruction_step_by_step()
    
    # 测试不同方向
    test_different_orientations()
    
    # 测试最小情况
    test_minimal_case()
    
    print("\n" + "=" * 60)
    print("诊断完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
