#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试坐标系是否正确
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加支持文件路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SysIden_AvgMethod_SupportFiles'))
from SysIden_AvgMethod_SupportFiles.k_functions import plotReconstruction

def test_coordinate_system():
    """测试坐标系"""
    print("=" * 60)
    print("测试坐标系是否正确")
    print("=" * 60)
    
    # 创建一个明显的方向性模式
    filterSize = 7
    numLags = 1
    
    # 创建一个"L"形滤波器
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    
    # L形：水平线在上，垂直线在左
    filterWeights[1, 1:5, 0] = 1.0    # 水平部分（第2行，列1-4）
    filterWeights[1:5, 1, 0] = 1.0    # 垂直部分（行1-4，第2列）
    
    print("原始L形滤波器:")
    print("应该显示：")
    print("  ■■■■")
    print("  ■")
    print("  ■")
    print("  ■")
    
    # 显示原始滤波器
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original L-shaped Filter')
    plt.colorbar()
    
    # 添加网格来清楚显示像素
    for i in range(filterSize + 1):
        plt.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
        plt.axvline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
    
    # 创建简单的映射权重
    mapSize = 2
    mapWeights = np.ones((mapSize, mapSize))
    
    plt.subplot(1, 3, 2)
    plt.imshow(mapWeights, cmap='hot', interpolation='nearest')
    plt.title('Map Weights')
    plt.colorbar()
    
    # 执行重构
    stride = 2
    poolSize = 1
    fullSize = 15
    
    print(f"\n重构参数:")
    print(f"  stride: {stride}")
    print(f"  poolSize: {poolSize}")
    print(f"  fullSize: {fullSize}")
    
    # 调用修复后的plotReconstruction函数
    reconFilter = plotReconstruction(filterWeights, mapWeights, stride, poolSize, fullSize)
    
    # 手动显示重构结果
    plt.subplot(1, 3, 3)
    plt.imshow(reconFilter[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed Filter')
    plt.colorbar()
    
    # 添加网格
    for i in range(0, fullSize + 1, 2):
        plt.axhline(i - 0.5, color='gray', linewidth=0.3, alpha=0.5)
        plt.axvline(i - 0.5, color='gray', linewidth=0.3, alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('coordinate_system_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 分析结果
    print(f"\n结果分析:")
    print(f"重构滤波器形状: {reconFilter.shape}")
    
    # 检查L形是否保持正确方向
    frame = reconFilter[:, :, 0]
    
    # 找到非零区域
    nonzero_positions = np.where(np.abs(frame) > 0.01)
    if len(nonzero_positions[0]) > 0:
        min_row, max_row = np.min(nonzero_positions[0]), np.max(nonzero_positions[0])
        min_col, max_col = np.min(nonzero_positions[1]), np.max(nonzero_positions[1])
        
        print(f"非零区域范围:")
        print(f"  行: {min_row} - {max_row}")
        print(f"  列: {min_col} - {max_col}")
        
        # 检查L形特征
        # 应该在左上角有一个L形
        top_row = frame[min_row, :]
        left_col = frame[:, min_col]
        
        top_nonzero = np.sum(np.abs(top_row) > 0.01)
        left_nonzero = np.sum(np.abs(left_col) > 0.01)
        
        print(f"顶部行非零元素数: {top_nonzero}")
        print(f"左侧列非零元素数: {left_nonzero}")
        
        if top_nonzero > 1 and left_nonzero > 1:
            print("✅ L形方向正确 - 坐标系修复成功！")
        else:
            print("❌ L形方向可能不正确")
    
    return reconFilter

def test_simple_arrow():
    """测试简单箭头形状"""
    print("\n" + "=" * 60)
    print("测试箭头形状")
    print("=" * 60)
    
    # 创建向右的箭头
    filterSize = 5
    numLags = 1
    
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    
    # 箭头主体（水平线）
    filterWeights[2, 1:4, 0] = 1.0
    # 箭头头部
    filterWeights[1, 3, 0] = 1.0
    filterWeights[3, 3, 0] = 1.0
    
    print("原始箭头滤波器（应该向右）:")
    print("   →")
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original Arrow (Right)')
    plt.colorbar()
    
    # 重构
    mapWeights = np.ones((2, 2))
    reconFilter = plotReconstruction(filterWeights, mapWeights, 1, 1, 10)
    
    plt.subplot(1, 3, 2)
    plt.imshow(reconFilter[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed Arrow')
    plt.colorbar()
    
    # 检查箭头方向
    frame = reconFilter[:, :, 0]
    
    # 计算水平和垂直方向的梯度
    grad_x = np.abs(np.diff(frame, axis=1)).sum()
    grad_y = np.abs(np.diff(frame, axis=0)).sum()
    
    plt.subplot(1, 3, 3)
    plt.bar(['Horizontal', 'Vertical'], [grad_x, grad_y])
    plt.title('Gradient Analysis')
    plt.ylabel('Gradient Magnitude')
    
    print(f"水平梯度: {grad_x:.2f}")
    print(f"垂直梯度: {grad_y:.2f}")
    
    if grad_x > grad_y:
        print("✅ 箭头保持水平方向 - 坐标系正确")
    else:
        print("❌ 箭头可能旋转了90度")
    
    plt.tight_layout()
    plt.savefig('arrow_test.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("坐标系测试工具")
    print("检查RF重构是否有90度旋转问题")
    
    # 测试L形
    test_coordinate_system()
    
    # 测试箭头
    test_simple_arrow()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("如果形状保持正确方向，说明坐标系修复成功")
    print("如果形状旋转了90度，需要进一步调试")
    print("=" * 60)

if __name__ == "__main__":
    main()
