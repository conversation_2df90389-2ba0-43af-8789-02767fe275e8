#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的RF绘制脚本
"""

import os
import sys
import numpy as np
import scipy.io as sio
from glob import glob

# 导入修复后的函数
from plot_rf_from_saved_data import safe_extract_filter_weights, find_result_files, load_cluster_results

def test_safe_extraction():
    """测试安全的权重提取函数"""
    print("=" * 50)
    print("测试安全权重提取函数")
    print("=" * 50)
    
    # 找到结果文件
    result_files = find_result_files(".")
    
    if len(result_files) == 0:
        print("没有找到结果文件")
        return
    
    print(f"找到 {len(result_files)} 个结果文件")
    
    # 测试第一个文件
    test_file = result_files[0]
    print(f"测试文件: {test_file}")
    
    data = load_cluster_results(test_file)
    if data is None:
        print("无法加载数据")
        return
    
    # 测试权重提取
    if 'weights_pass1' in data:
        weights_pass1 = data['weights_pass1']
        print(f"weights_pass1 类型: {type(weights_pass1)}")
        
        # 使用安全提取函数
        filterWeights = safe_extract_filter_weights(weights_pass1)
        
        if filterWeights is not None:
            print(f"✅ 成功提取滤波器权重")
            print(f"  形状: {filterWeights.shape}")
            print(f"  数据类型: {filterWeights.dtype}")
            print(f"  值范围: [{np.min(filterWeights):.4f}, {np.max(filterWeights):.4f}]")
            
            # 检查是否有有效数据
            if np.any(np.abs(filterWeights) > 1e-10):
                print(f"  ✅ 包含有效的非零数据")
            else:
                print(f"  ⚠️  所有值都接近零")
        else:
            print(f"❌ 无法提取滤波器权重")
    else:
        print("数据中没有weights_pass1")
    
    # 测试其他关键数据
    key_data = ['Final_Rf_Construct', 'VAF', 'gaussian_map']
    for key in key_data:
        if key in data:
            value = data[key]
            print(f"{key}: 形状={getattr(value, 'shape', 'N/A')}, 类型={type(value)}")
        else:
            print(f"{key}: 不存在")

def test_single_cluster_plot():
    """测试单个cluster的绘制"""
    print("\n" + "=" * 50)
    print("测试单个cluster绘制")
    print("=" * 50)
    
    # 找到结果文件
    result_files = find_result_files(".")
    
    if len(result_files) == 0:
        print("没有找到结果文件")
        return
    
    # 选择第一个文件
    test_file = result_files[0]
    data = load_cluster_results(test_file)
    
    if data is None:
        print("无法加载数据")
        return
    
    cluster_id = int(data['cluster_id'])
    print(f"测试绘制 Cluster {cluster_id}")
    
    try:
        # 导入绘制函数
        from plot_rf_from_saved_data import plot_rf_components
        
        # 尝试绘制
        save_dir = plot_rf_components(cluster_id, data)
        print(f"✅ 成功绘制，保存到: {save_dir}")
        
    except Exception as e:
        print(f"❌ 绘制失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("RF绘制脚本修复测试")
    print("=" * 60)
    
    # 测试权重提取
    test_safe_extraction()
    
    # 测试绘制
    test_single_cluster_plot()
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()
