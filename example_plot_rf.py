#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RF绘制工具的使用示例
"""

import os
import sys
from plot_rf_from_saved_data import find_result_files, load_cluster_results, plot_rf_components, plot_multiple_clusters_summary

def quick_plot_example():
    """快速绘制示例"""
    print("=" * 50)
    print("RF绘制工具快速使用示例")
    print("=" * 50)
    
    # 1. 查找所有结果文件
    result_files = find_result_files(".")
    print(f"找到 {len(result_files)} 个结果文件")
    
    if len(result_files) == 0:
        print("没有找到结果文件，请确保运行了 process_all_neurons.py")
        return
    
    # 2. 显示前几个cluster的信息
    print("\n前5个cluster的信息:")
    for i, file in enumerate(result_files[:5]):
        data = load_cluster_results(file)
        if data:
            cluster_id = int(data['cluster_id'])
            vaf = float(data['VAF'])
            print(f"  Cluster {cluster_id}: VAF = {vaf:.2f}%")
    
    # 3. 选择VAF最高的cluster进行详细绘制
    best_cluster = None
    best_vaf = -1
    best_file = None
    
    for file in result_files:
        data = load_cluster_results(file)
        if data:
            vaf = float(data['VAF'])
            if vaf > best_vaf:
                best_vaf = vaf
                best_cluster = int(data['cluster_id'])
                best_file = file
    
    if best_cluster is not None:
        print(f"\n绘制VAF最高的cluster {best_cluster} (VAF: {best_vaf:.2f}%)")
        data = load_cluster_results(best_file)
        plot_rf_components(best_cluster, data)
    
    # 4. 生成前10个cluster的总结图
    print(f"\n生成前10个cluster的总结图...")
    plot_multiple_clusters_summary(result_files[:10])
    
    print("\n示例完成！")

if __name__ == "__main__":
    quick_plot_example()
