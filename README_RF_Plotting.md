# 感受野(RF)绘制工具使用说明

这个工具包可以从保存的数值数据中读取并绘制神经元的感受野(Receptive Field, RF)。

## 文件说明

- `plot_rf_from_saved_data.py`: 主要的RF绘制脚本
- `example_plot_rf.py`: 快速使用示例
- `README_RF_Plotting.md`: 本说明文件

## 前提条件

确保你已经运行了 `process_all_neurons.py` 脚本，它会生成包含RF数据的 `.mat` 文件。

## 快速开始

### 1. 查看可用的cluster

```bash
python plot_rf_from_saved_data.py --list
```

这会显示所有找到的cluster及其VAF值。

### 2. 快速示例

```bash
python example_plot_rf.py
```

这会自动找到VAF最高的cluster并绘制其RF，同时生成前10个cluster的总结图。

### 3. 绘制所有cluster

```bash
python plot_rf_from_saved_data.py
```

这会为每个cluster生成详细的RF图像，并创建一个总结图。

## 详细使用方法

### 绘制指定cluster的RF

```bash
python plot_rf_from_saved_data.py --cluster_id 123
```

### 只生成总结图

```bash
python plot_rf_from_saved_data.py --summary_only
```

### 限制总结图中的cluster数量

```bash
python plot_rf_from_saved_data.py --summary_only --max_clusters 5
```

### 指定搜索目录

```bash
python plot_rf_from_saved_data.py --base_dir ./results
```

## 输出说明

脚本会生成以下类型的图像：

1. **原始滤波器权重**: 显示CNN第一层学到的滤波器权重（多个时间帧）
2. **重构的感受野**: 经过高斯映射重构后的完整感受野
3. **高斯映射**: 显示空间注意力分布
4. **PReLU Alpha值**: 显示激活函数的参数

## 输出文件结构

```
./rf_plots/
├── cluster_123/
│   ├── cluster_123_filter_weights.png
│   ├── cluster_123_reconstructed_rf.png
│   ├── cluster_123_gaussian_map.png
│   └── cluster_123_prelu_alpha.png
├── cluster_456/
│   └── ...
└── clusters_summary.png
```

## 数据格式要求

脚本期望找到以下格式的 `.mat` 文件：
- 文件名模式: `cluster_*_results.mat`
- 包含的数据字段:
  - `cluster_id`: cluster编号
  - `weights_pass1`: 第一阶段训练的权重
  - `weights_pass2`: 第二阶段训练的权重
  - `Final_Rf_Construct`: 重构的感受野
  - `VAF`: 方差解释比例
  - `gaussian_map`: 高斯映射

## 颜色映射说明

- **RF图像**: 使用 `RdBu_r` 颜色映射，红色表示正值，蓝色表示负值
- **高斯映射**: 使用 `hot` 颜色映射，显示空间权重分布

## 故障排除

1. **找不到结果文件**: 确保已运行 `process_all_neurons.py` 并生成了结果文件
2. **导入错误**: 确保 `SysIden_AvgMethod_SupportFiles` 目录在正确位置
3. **内存不足**: 如果cluster数量很多，可以使用 `--max_clusters` 限制处理数量

## 自定义绘制

你可以直接导入 `plot_rf_from_saved_data.py` 中的函数来进行自定义绘制：

```python
from plot_rf_from_saved_data import load_cluster_results, plot_rf_components

# 加载特定cluster的数据
data = load_cluster_results('path/to/cluster_123_results.mat')

# 绘制RF
plot_rf_components(123, data, save_dir='./custom_output')
```
