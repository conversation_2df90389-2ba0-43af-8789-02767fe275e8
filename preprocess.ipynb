{"cells": [{"cell_type": "code", "execution_count": 5, "id": "altered-carolina", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████| 72000/72000 [00:05<00:00, 14006.77it/s]\n"]}], "source": ["import os\n", "import math\n", "import h5py\n", "import pickle\n", "import argparse\n", "import numpy as np\n", "from tqdm import tqdm\n", "from torch.utils.data import Dataset\n", "\n", "\n", "def preprocess(lag, bin_size, bin_cnt):\n", "    origin_neu_file = 'PLCATD001_Ephys_DA07_Image_0.mat'\n", "    origin_img_file = 'Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35).mat'\n", "    \n", "    with h5py.File(f'./{origin_img_file}', 'r') as mat_file:\n", "        np_img = np.array([mat_file[i][:] for i in mat_file['imgset']])\n", "\n", "    with h5py.File(f'./{origin_neu_file}', 'r') as mat_file:\n", "        np_on_time = np.array([mat_file[i][0, 0] for i in mat_file['ex']['CondTest']['Command_COND'][:, 0]])\n", "        np_off_time = mat_file['ex']['CondTest']['CondOff'][:, 0]\n", "        np_img_id = mat_file['ex']['CondTest']['CondIndex'][:, 0] - 1\n", "        np_spiking_cluster_id = mat_file['spike0_kilosort3']['cluster'][0]\n", "        np_spiking_time = mat_file['spike0_kilosort3']['time'][0]\n", "        map_cluster_id = {v: i for i, v in enumerate(mat_file['spike0_kilosort3']['clusterid'][:][0])}\n", "\n", "    np_spike_cnt = np.zeros([len(np_img_id), bin_cnt, len(map_cluster_id)])  # img * repetition * bin * cluster\n", "    left, right = 0, 0\n", "\n", "    for i in tqdm(range(len(np_on_time))):\n", "        t0 = np_on_time[i] + lag\n", "        t1 = t0 + bin_size * bin_cnt\n", "\n", "        while right < len(np_spiking_time) and np_spiking_time[right] <= t1:\n", "            right += 1\n", "        while left < right and np_spiking_time[left] < t0:\n", "            left += 1\n", "\n", "        for j in range(left, right):\n", "            t = math.floor((np_spiking_time[j] - t0) / bin_size)\n", "            t = min(max(t, 0), bin_cnt - 1)\n", "            k = map_cluster_id[np_spiking_cluster_id[j]]\n", "            np_spike_cnt[i, t, k] += 1\n", "\n", "    with open('./data.pkl', 'wb') as f:\n", "        pickle.dump(np_img[np_img_id], f)\n", "        pickle.dump(np_spike_cnt, f)\n", "        \n", "preprocess(0, 20, 7)"]}, {"cell_type": "code", "execution_count": 6, "id": "wooden-proof", "metadata": {}, "outputs": [], "source": ["with open('./data.pkl', 'rb') as f:\n", "    np_img = pickle.load(f)\n", "    np_spike = pickle.load(f)"]}, {"cell_type": "code", "execution_count": 7, "id": "indian-business", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(1278)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["(np_spike.sum(axis=1) == 0).sum(axis=1).max()"]}, {"cell_type": "code", "execution_count": 10, "id": "educational-recommendation", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|█████████████████████████████████████████████████████████████████████████| 72000/72000 [00:01<00:00, 70800.81it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["(72000, 64, 64) (72000, 1278)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import h5py\n", "import numpy as np\n", "from tqdm import tqdm\n", "origin_neu_file = {'da7': 'PLCATD001_Ephys_DA07_Image_0.mat'}\n", "# origin_neu_file = {'d2': 'PLCAT009_Ephys_d2_Image_0.mat',\n", "#                    'f1': 'PLCAT011_Ephys_f1_Image_0.mat',\n", "#                    'f13': 'PLCAT011_Ephys_f13_Image_0.mat'}\n", "origin_img_file = 'Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35).mat'\n", "\n", "def load_from_origin_file(trial, time_lag, time_bin):\n", "    with h5py.File(f'./{origin_neu_file[trial]}', 'r') as mat_file:\n", "        # np_on_time = mat_file['ex']['CondTest']['CondOn'][:, 0]\n", "        np_on_time = np.array([mat_file[i][0, 0] for i in mat_file['ex']['CondTest']['Command_COND'][:, 0]])\n", "        np_off_time = mat_file['ex']['CondTest']['CondOff'][:, 0]\n", "        np_img_id = mat_file['ex']['CondTest']['CondIndex'][:, 0] - 1\n", "        np_spiking_cluster_id = mat_file['spike0_kilosort3']['cluster'][0]\n", "        np_spiking_time = mat_file['spike0_kilosort3']['time'][0]\n", "        map_cluster_id = {v: i for i, v in enumerate(mat_file['spike0_kilosort3']['clusterid'][:][0])}\n", "\n", "    with h5py.File(f'./{origin_img_file}', 'r') as mat_file:\n", "        np_img = np.array([mat_file[i][:] for i in mat_file['imgset']])\n", "    np_img = np_img[np_img_id]\n", "\n", "    np_spike = np.zeros([len(np_img_id), len(map_cluster_id)])\n", "    left, right = 0, 0\n", "    sliding_window = np.zeros(len(map_cluster_id))\n", "    for i in tqdm(range(len(np_off_time))):\n", "        while right < len(np_spiking_time) and np_spiking_time[right] <= np_off_time[i] + time_lag:\n", "            sliding_window[map_cluster_id[np_spiking_cluster_id[right]]] += 1\n", "            right += 1\n", "        while left < right and np_spiking_time[left] < np_off_time[i] + time_lag - time_bin:\n", "            sliding_window[map_cluster_id[np_spiking_cluster_id[left]]] -= 1\n", "            left += 1\n", "        np_spike[i, :] = sliding_window\n", "        \n", "    return np_img, np_spike, (np_on_time, np_off_time, np_img_id, map_cluster_id)\n", "\n", "\n", "np_img_all, np_spike_all, (np_on_time, np_off_time, np_img_id, map_cluster_id) = load_from_origin_file('da7', 0, 80)\n", "print(np_img_all.shape, np_spike_all.shape)"]}, {"cell_type": "code", "execution_count": 11, "id": "random-contest", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA9wAAAHqCAYAAAD27EaEAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjEsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvc2/+5QAAAAlwSFlzAAAPYQAAD2EBqD+naQAAaQNJREFUeJzt3X1cVHX+///nyMVwEYwCwkAiWqumQuVFedUmpuK1lZW6GummZmkWqbWZnxL7lJStFy1mV2tqXqTbr2xr20wstVy1vKw0P2ZFiAVBhCCKw9X5/dHXs42AysjAoI/77XZued7v1znzfp+Z6c1rzjnvYzEMwxAAAAAAAKhVjeq7AQAAAAAAXIxIuAEAAAAAcAMSbgAAAAAA3ICEGwAAAAAANyDhBgAAAADADUi4AQAAAABwAxJuAAAAAADcgIQbAAAAAAA3IOEGAAAAAMANSLhxSVm2bJksFot27dpVZf3gwYPVokULp7IWLVpo7NixNXqdbdu2KTk5WceOHXOtoZegtWvXqn379vL395fFYtG+ffuqjNu8ebMsFou5+Pr6qmnTpurRo4dmzpypjIyMum14FRYvXqxly5ZVKv/hhx9ksViqrAOAiwHjrOc633FWkg4ePKjExERdccUV8vPzU1hYmDp27Kj7779fhYWFLr2+xWJRcnKyuX6uz8ppycnJslgsLr3mhTr92qcXHx8fNW/eXBMmTFB2drZL+zx58qSSk5O1efPm2m0sPJZ3fTcA8HTr1q1TcHBwjbbZtm2bZs+erbFjx6px48buadhFJDc3V4mJierfv78WL14sq9Wq1q1bn3WbOXPmqFevXiovL1deXp4+++wzvfbaa1qwYIFeffVVjR49uo5aX9nixYsVFhZW6Q/IyMhIbd++XVdeeWX9NAwAPBDjrPvVZJzdu3evevToobZt2+qJJ55QixYt9Msvv+iLL77QmjVrNH369Bq/X5K0fft2NWvWrMbbjR8/Xv3796/xdrVp/fr1stlsKioq0oYNGzRv3jxt27ZN+/btk4+PT432dfLkSc2ePVuSFB8f74bWwtOQcAPn0KFDh/puQo2VlpbKYrHI27thfMW/+eYblZaW6s4771TPnj3Pa5tWrVqpa9eu5vrQoUM1bdo09enTR2PHjtXVV1+tuLi4C26bYRg6deqU/P39L3hfVqvVqc0AAMbZulCTcXbhwoVq1KiRNm/erKCgILP89ttv1//+7//KMAyX2uDq+NesWTOXEvXa1KlTJ4WFhUmS+vTpo19++UVLly7V1q1b1atXr3ptGzwfl5QD53DmpW4VFRV66qmn1KZNG/n7+6tx48a6+uqr9fzzz0v67fKjhx9+WJLUsmVL8zKk05cOVVRUaO7cubrqqqtktVoVHh6uu+66S0ePHnV6XcMwNGfOHMXExMjPz0+dO3dWWlqa4uPjnX4RPX2J9YoVKzRt2jRdfvnlslqt+vbbb5Wbm6tJkyapXbt2uuyyyxQeHq6bbrpJn376qdNrnb7U+bnnntOzzz6rFi1ayN/fX/Hx8eYg/eijjyoqKko2m0233nqrcnJyzuv4vfvuu+rWrZsCAgIUFBSkvn37avv27Wb92LFjdcMNN0iSRowYIYvF4vIvviEhIXr55ZdVVlamBQsWOL3GmZcwSlVfpmaxWHT//ffrpZdeUtu2bWW1WrV8+XJJ0uzZs9WlSxeFhIQoODhYHTt21JIlS5z++GjRooUOHDigLVu2mO/96deu7pLyrVu3qnfv3goKClJAQIC6d++u999/3ynm9KV3mzZt0n333aewsDCFhoZq2LBh+umnn1w6XgDgCRhnPWuczcvLU3BwsC677LIq638/bsbHxys2NlaffvqpunbtKn9/f11++eV6/PHHVV5eXmm7319SXpWsrCx16tRJrVq10uHDhyVVPVa3aNFCgwcP1vr169WxY0f5+/vrqquu0muvvVZpn1u3blW3bt3k5+dntu3vf/+7LBaLfvjhh7O2pzqdO3eWJP38889m2fl8Fn744Qc1bdpU0m9/U5z+7P7+83/48GGNGjVK4eHhslqtatu2rV544QWX2gnP0DB+lgNqWXl5ucrKyiqVn8+vtnPnzlVycrL+53/+RzfeeKNKS0v1f//3f+Z9ZOPHj9evv/6q1NRUvf3224qMjJQktWvXTpJ033336ZVXXtH999+vwYMH64cfftDjjz+uzZs3a8+ePeYvqDNnzlRKSoruueceDRs2TJmZmRo/frxKS0urvAxsxowZ6tatm1566SU1atRI4eHhys3NlSTNmjVLdrtdRUVFWrduneLj4/XRRx9VGnBfeOEFXX311XrhhRd07NgxTZs2TUOGDFGXLl3k4+Oj1157TRkZGZo+fbrGjx+vd99996zHavXq1Ro9erQSEhL0xhtvyOFwaO7cuebr33DDDXr88cd1/fXXa/LkyeZl4q5cqnbaddddp8jISH3yyScu7+Odd97Rp59+qieeeEJ2u13h4eGSfhsoJ06cqObNm0uSduzYoSlTpujHH3/UE088Iem3SyNvv/122Ww2LV68WNJvZ7ars2XLFvXt21dXX321lixZIqvVqsWLF2vIkCF64403NGLECKf48ePHa9CgQVq9erUyMzP18MMP684779THH3/scn8BoLYxzjbccbZbt256//33NXr0aE2cOFHXX3/9Wa/yys7O1siRI/Xoo4/qySef1Pvvv6+nnnpK+fn5WrRo0Vnb/3v79+/XwIED1axZM23fvt18n6rzxRdfaNq0aXr00UcVERGhv//97xo3bpz+8Ic/6MYbb5Qkffnll+rbt69at26t5cuXKyAgQC+99JJWrlx53u2qSnp6uiQ5fU5+/fVXSWf/LERGRmr9+vXq37+/xo0bp/Hjx0uSmYR//fXX6t69u5o3b6558+bJbrfrww8/1AMPPKBffvlFs2bNuqB2o54YwCVk6dKlhqSzLjExMU7bxMTEGGPGjDHXBw8ebFx77bVnfZ3nnnvOkGSkp6c7lR88eNCQZEyaNMmp/LPPPjMkGY899phhGIbx66+/Glar1RgxYoRT3Pbt2w1JRs+ePc2yTZs2GZKMG2+88Zz9LysrM0pLS43evXsbt956q1menp5uSDKuueYao7y83CxfuHChIckYOnSo036SkpIMSUZBQUG1r1VeXm5ERUUZcXFxTvs8fvy4ER4ebnTv3r1SH958881z9uF8Yrt06WL4+/ub62PGjKn0vhqGYcyaNcs483+DkgybzWb8+uuvZ21HeXm5UVpaajz55JNGaGioUVFRYda1b9/e6T067fRxXrp0qVnWtWtXIzw83Dh+/LhZVlZWZsTGxhrNmjUz93v6s3vmZ2fu3LmGJCMrK+us7QWAusA42/DH2VOnThm33HKL+X55eXkZHTp0MGbOnGnk5OQ4xfbs2dOQZPzzn/90Kp8wYYLRqFEjIyMjwyyTZMyaNctcP/1Z2blzp5GWlmYEBwcbt99+u1FcXOy0r6rG6piYGMPPz89p/8XFxUZISIgxceJEs+yOO+4wAgMDjdzcXKfj1q5duyo/P2c6/drZ2dlGaWmpkZ+fb/zjH/8wAgMDjT/96U9n3ba6z0Jubm6lY3Fav379jGbNmlV63++//37Dz8/vnH+bwDNxSTkuSa+//rp27txZaTl9ydXZXH/99friiy80adIkffjhhzWarXPTpk2SVGkyreuvv15t27bVRx99JOm3M6cOh0PDhw93iuvatWuVl0ZL0m233VZl+UsvvaSOHTvKz89P3t7e8vHx0UcffaSDBw9Wih04cKAaNfrv/xbatm0rSRo0aJBT3OnyI0eOVNNT6dChQ/rpp5+UmJjotM/LLrtMt912m3bs2KGTJ09Wu/2FMFy8v+y0m266SU2aNKlU/vHHH6tPnz6y2Wzy8vKSj4+PnnjiCeXl5Z33pX+/d+LECX322We6/fbbnS7d8/LyUmJioo4ePapDhw45bTN06FCn9auvvlqSPGJ2dgA4jXG24Y6zVqtV69at09dff60FCxZo5MiRys3N1dNPP622bdtWGpeCgoIqjU2jRo1SRUXFeV1ttnz5cg0cOFDjx4/XP/7xD/n5+Z1XO6+99lrzijNJ8vPzU+vWrZ3Gwy1btuimm25yOlveqFGjSu/7udjtdvn4+KhJkyYaPny4OnXqZN5u9ns1+Syc6dSpU/roo4906623KiAgQGVlZeYycOBAnTp1Sjt27KhRu+EZSLhxSWrbtq06d+5cabHZbOfcdsaMGfrrX/+qHTt2aMCAAQoNDVXv3r3P+VgL6bf7oiSZl7/9XlRUlFl/+r8RERGV4qoqq26f8+fP13333acuXbrorbfe0o4dO7Rz5071799fxcXFleJDQkKc1n19fc9afurUqSrb8vs+VNfXiooK5efnV7v9hThy5IiioqJc3r6qNn/++edKSEiQJL366qv6z3/+o507d2rmzJmSVOXxPJf8/HwZhlHtMZL+exxPCw0NdVo/fbm6K68PAO7CONvwx9m2bdsqKSlJK1eu1JEjRzR//nzl5eXp8ccfd4qr6njZ7XanNp7NmjVr5O/vr/Hjx9fo8V9njofSb2Pi7497Xl5ejd7j6mzcuFE7d+7Uhx9+qNtuu02ffPKJpkyZ4hRT08/CmfLy8lRWVqbU1FT5+Pg4LQMHDpQk/fLLLzVqNzwD93ADNeTt7a2pU6dq6tSpOnbsmDZu3KjHHntM/fr1U2ZmpgICAqrd9vTgkJWVVWnGzZ9++sn8BfZ03O8n4zgtOzu7yl/fqxqkVq5cqfj4eL344otO5cePHz97J2vB7/t6pp9++kmNGjWq8izyhfr888+VnZ2tcePGmWV+fn5yOByVYqsbuKo6lmvWrJGPj4/+9a9/Of36/s4777jc1iZNmqhRo0bVHiNJ57yHDQAuNoyz56cux1mLxaKHHnpITz75pPbv3+9UV90x/H0bz2bVqlV6/PHH1bNnT23YsEHXXnttrbT59OufrX3n65prrjE/O3379lW/fv30yiuvaNy4cbruuuskXfhnoUmTJuYVbpMnT64ypmXLljVqNzwDZ7iBC9C4cWPdfvvtmjx5sn799VdztsvqzjredNNNklRpso6dO3fq4MGD6t27tySpS5cuslqtWrt2rVPcjh07anTpsMViqTRh15dffuk0e6m7tGnTRpdffrlWr17tdIn3iRMn9NZbb5kzqtamX3/9Vffee698fHz00EMPmeUtWrRQTk6O06BbUlKiDz/88Lz3ffrxL15eXmZZcXGxVqxYUSn2zF/YqxMYGKguXbro7bffdoqvqKjQypUr1axZs3M+jxwALmaMs9Vz1zhbVQIv/ZbEFxYWVrqC7Pjx45Umd1u9erUaNWpkTl52NiEhIdq4caPatm2rXr161epl0z179tTHH3/s9AN7RUWF3nzzTZf3abFY9MILL8jLy0v/8z//41R+Pp+F6j67AQEB6tWrl/bu3aurr766yitEzucHDHgeznADNTRkyBDFxsaqc+fOatq0qTIyMrRw4ULFxMSoVatWkmQ+//n555/XmDFj5OPjozZt2qhNmza65557lJqaqkaNGmnAgAHm7KnR0dFmkhgSEqKpU6cqJSVFTZo00a233qqjR49q9uzZioyMdLpX62wGDx6s//3f/9WsWbPUs2dPHTp0SE8++aRatmxZ5eyxtalRo0aaO3euRo8ercGDB2vixIlyOBx67rnndOzYMT3zzDMXtP/Dhw9rx44dqqioUF5enj777DMtWbJEhYWFev3119W+fXszdsSIEXriiSc0cuRIPfzwwzp16pT+9re/VXpkydkMGjRI8+fP16hRo3TPPfcoLy9Pf/3rX6ucgTwuLk5r1qzR2rVrdcUVV8jPz6/aZ4KnpKSob9++6tWrl6ZPny5fX18tXrxY+/fv1xtvvFGjy+sA4GLAOHt+3DXO3nPPPTp27Jhuu+02xcbGysvLS//3f/+nBQsWqFGjRvrLX/7iFB8aGqr77rtPR44cUevWrfXvf/9br776qu677z6ne6zPJigoSOvXr9ewYcPUt29fvfvuu7XyfOuZM2fqvffeU+/evTVz5kz5+/vrpZde0okTJyTpvN/nM7Vq1Ur33HOPFi9erK1bt+qGG244789CUFCQYmJi9M9//lO9e/dWSEiIwsLC1KJFCz3//PO64YYb9Mc//lH33XefWrRooePHj+vbb7/Ve++9xxNJGqr6nbMNqFu/nxGzKoMGDTrn7Knz5s0zunfvboSFhRm+vr5G8+bNjXHjxhk//PCD03YzZswwoqKijEaNGhmSjE2bNhmG8dvsmM8++6zRunVrw8fHxwgLCzPuvPNOIzMz02n7iooK46mnnjKaNWtm+Pr6GldffbXxr3/9y7jmmmucZrs828yjDofDmD59unH55Zcbfn5+RseOHY133nmn0qzdp2dPfe6555y2r27f5zqOv/fOO+8YXbp0Mfz8/IzAwECjd+/exn/+85/zep2qnI49vXh7exuhoaFGt27djMcee6zS+3Dav//9b+Paa681/P39jSuuuMJYtGhRtbOUT548ucp9vPbaa0abNm0Mq9VqXHHFFUZKSoqxZMmSSjOd/vDDD0ZCQoIRFBTkNCNvVbOUG4ZhfPrpp8ZNN91kBAYGGv7+/kbXrl2N9957zymmumN++nic/nwBQH1inG344+yHH35o3H333Ua7du0Mm81meHt7G5GRkcawYcOM7du3O8X27NnTaN++vbF582ajc+fOhtVqNSIjI43HHnvMKC0tdYrVWWYpP83hcBi33Xab4efnZ7z//vuGYVQ/S/mgQYMqtb1nz56VnhLy6aefGl26dDGsVqtht9uNhx9+2Hj22WcNScaxY8fOeixOv/bvZzk/7eeffzYuu+wyo1evXmbbz+ezYBiGsXHjRqNDhw6G1Wo1JDl9/tPT0427777buPzyyw0fHx+jadOmRvfu3Y2nnnrqrG2F57IYxgVO5wugzqSnp+uqq67SrFmz9Nhjj9V3cwAAuKgwztZMfHy8fvnll0r3dXu6hIQE/fDDD/rmm2/quym4BHBJOeChvvjiC73xxhvq3r27goODdejQIc2dO1fBwcFOE4IBAICaY5y9NEydOlUdOnRQdHS0fv31V61atUppaWlasmRJfTcNlwgSbsBDBQYGateuXVqyZImOHTsmm82m+Ph4Pf300zV+nAUAAHDGOHtpKC8v1xNPPKHs7GxZLBa1a9dOK1as0J133lnfTcMlgkvKAQAAAABwAx4LBgAAAACAG5BwAwAAAADgBiTcAAAAAAC4AZOmnaeKigr99NNPCgoKksViqe/mAAAuMoZh6Pjx44qKilKjRvweXlOM0wAAd3J1nCbhPk8//fSToqOj67sZAICLXGZmppo1a1bfzWhwGKcBAHWhpuM0Cfd5CgoKkvTbAQ4ODq7n1gAALjaFhYWKjo42xxvUDOM0AMCdXB2nSbjP0+nL04KDgxnIAQBuw+XQrmGcBgDUhZqO09wkBgAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABu4F3fDQAAoDbk5uaqsLCwyrrg4GA1bdq0jluEhupsn6X6xOcYABoeEm4AQIOXm5urUaPuU16eo8r60FCrVq9+kWQF53Suz1J94nMMAA0PCTcAoMErLCxUXp5DVus0+ftHO9UVF2cqL2+eCgsLSVRwTmf7LNUnPscA0DCRcAMALhr+/tEKDLyyUrnD805WwsNV91mqT3yOAaDhYdI0AAAAAADcgDPcAIBL3rkmyWKyKgAA4AoSbgDAJe18JslisioAAOAKEm4AwCXtXJNkMVkVAABwFQk3AAA6+yRZTFYFAABcwaRpAAAAAAC4AQk3AAAAAABuQMINAACq9Mknn2jIkCGKioqSxWLRO++8Y9aVlpbqL3/5i+Li4hQYGKioqCjddddd+umnn5z24XA4NGXKFIWFhSkwMFBDhw7V0aNHnWLy8/OVmJgom80mm82mxMREHTt2rA56CACAe9Vrwn22gVySLBZLlctzzz1nxsTHx1eqHzlypNN+GMgBAKi5EydO6JprrtGiRYsq1Z08eVJ79uzR448/rj179ujtt9/WN998o6FDhzrFJSUlad26dVqzZo22bt2qoqIiDR48WOXl5WbMqFGjtG/fPq1fv17r16/Xvn37lJiY6Pb+AQDgbvU6adrpgfzPf/6zbrvttkr1WVlZTusffPCBxo0bVyl2woQJevLJJ811f39/p/pRo0bp6NGjWr9+vSTpnnvuUWJiot57773a6goAABedAQMGaMCAAVXW2Ww2paWlOZWlpqbq+uuv15EjR9S8eXMVFBRoyZIlWrFihfr06SNJWrlypaKjo7Vx40b169dPBw8e1Pr167Vjxw516dJFkvTqq6+qW7duOnTokNq0aePeTgIA4Eb1mnCfbSCXJLvd7rT+z3/+U7169dIVV1zhVB4QEFAp9jQGcgAA6kZBQYEsFosaN24sSdq9e7dKS0uVkJBgxkRFRSk2Nlbbtm1Tv379tH37dtlsNnOMlqSuXbvKZrNp27ZtjNMAgAatwdzD/fPPP+v999/XuHHjKtWtWrVKYWFhat++vaZPn67jx4+bdecayAEAwIU7deqUHn30UY0aNUrBwcGSpOzsbPn6+qpJkyZOsREREcrOzjZjwsPDK+0vPDzcjKmKw+FQYWGh0wIAgKdpMM/hXr58uYKCgjRs2DCn8tGjR6tly5ay2+3av3+/ZsyYoS+++MK8zO1CBnLH7x68ykAO4FKRm5t71v/nBQcHq2nTpnXYIni60tJSjRw5UhUVFVq8ePE54w3DkMViMdd//+/qYs6UkpKi2bNnu9ZgAADqSINJuF977TWNHj1afn5+TuUTJkww/x0bG6tWrVqpc+fO2rNnjzp27CiJgRwAzldubq5GjbpPeXmOamNCQ61avfpFkm5I+i3ZHj58uNLT0/Xxxx+bZ7el324NKykpUX5+vtNZ7pycHHXv3t2M+fnnnyvtNzc3VxEREdW+7owZMzR16lRzvbCwUNHR0bXRJQAAak2DSLg//fRTHTp0SGvXrj1nbMeOHeXj46PDhw+rY8eODOQAUAOFhYXKy3PIap0mf//K/88rLs5UXt48FRYWNqiEu7TUoYyMjCrrMjIyVFZWVsctujicTrYPHz6sTZs2KTQ01Km+U6dO8vHxUVpamoYPHy7ptwlR9+/fr7lz50qSunXrpoKCAn3++ee6/vrrJUmfffaZCgoKzKS8KlarVVar1U09AwCgdjSIhHvJkiXq1KmTrrnmmnPGHjhwQKWlpYqMjJTEQA4ArvD3j1Zg4JVV1jmqP/l9TvVxuXpJSZ4yMr7XlCnPVPn/dYfjhDIzf5bNdgEdu0gVFRXp22+/NdfT09O1b98+hYSEKCoqSrfffrv27Nmjf/3rXyovLzdv1QoJCZGvr69sNpvGjRunadOmKTQ0VCEhIZo+fbri4uLMWcvbtm2r/v37a8KECXr55Zcl/fY0kcGDBzNhGgCgwavXhPtsA3nz5s0l/Xa25c0339S8efMqbf/dd99p1apVGjhwoMLCwvT1119r2rRp6tChg3r06CGJgRwAPEV9Xa5eXl6ksjJf+fo+pMaNW1eqz8/fobKyp1VWVl7F1pe2Xbt2qVevXub66Su/xowZo+TkZL377ruSpGuvvdZpu02bNik+Pl6StGDBAnl7e2v48OEqLi5W7969tWzZMnl5eZnxq1at0gMPPGDOZj506NAqn/0NAEBDU68J99kG8mXLlkmS1qxZI8Mw9Kc//anS9r6+vvroo4/0/PPPq6ioSNHR0Ro0aJBmzZrFQA4AHqa+L1f382tW5Vn74uKqLzWHFB8fL8Mwqq0/W91pfn5+Sk1NVWpqarUxISEhWrlypUttBADAk9Vrwn2ugVz67Wz0PffcU2VddHS0tmzZcs7XYSAHAM/hrsvVAQAAPE2DeQ43AAAAAAANCQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGDeI53ACAmqmP510DAADAGQk3AFxk6ut51wAAAHBGwg0AF5n6ft41AAAAfkPCDQAXKZ53DQAAUL+YNA0AAAAAADcg4QYAAAAAwA1IuAEAAAAAcAMSbgAAAAAA3ICEGwAAAAAAN2CWcgBAjZSWOpSRkVFlXXBwMI8aAwAA+H9IuAEA562kJE8ZGd9rypRnZLVaK9WHhlq1evWLJN0AAAAi4QYA1EB5eZHKynzl6/uQGjdu7VRXXJypvLx5KiwsJOEGAAAQCTcAwAV+fs0UGHhlpXKHox4aAwAA4KGYNA0AAAAAADfgDDcAXIKqm/gsIyNDZWVl9dAiAACAiw8JNwBcYs428ZnDcUKZmT/LZuPacAAAgAtFwg0Al5izTXyWn79DZWVPq6ysvJ5aBwAAcPEg4QaAS1RVE58VF1f9fG0AAADUHJOmAQAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG7Ac7gBALWmtNShjIyqn+WdkZGhsrKyOm4RAABA/SHhBgDUipKSPGVkfK8pU56R1WqtVO9wnFBm5s+y2Rz10DoAAIC6R8INAKgV5eVFKivzla/vQ2rcuHWl+vz8HSore1plZeX10DoAAIC6R8INAKhVfn7NFBh4ZaXy4uKqLzUHAAC4WDFpGgAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Qrwn3J598oiFDhigqKkoWi0XvvPOOU/3YsWNlsViclq5duzrFOBwOTZkyRWFhYQoMDNTQoUN19OhRp5j8/HwlJibKZrPJZrMpMTFRx44dc3PvAAAAAACXMu/6fPETJ07ommuu0Z///GfddtttVcb0799fS5cuNdd9fX2d6pOSkvTee+9pzZo1Cg0N1bRp0zR48GDt3r1bXl5ekqRRo0bp6NGjWr9+vSTpnnvuUWJiot577z039QwA4IrSUocyMjKqrAsODlbTpk3ruEW/8dR2AQAAz1avCfeAAQM0YMCAs8ZYrVbZ7fYq6woKCrRkyRKtWLFCffr0kSStXLlS0dHR2rhxo/r166eDBw9q/fr12rFjh7p06SJJevXVV9WtWzcdOnRIbdq0qd1OAQBcUlKSp4yM7zVlyjOyWq2V6kNDrVq9+sU6T249tV0AAMDz1WvCfT42b96s8PBwNW7cWD179tTTTz+t8PBwSdLu3btVWlqqhIQEMz4qKkqxsbHatm2b+vXrp+3bt8tms5nJtiR17dpVNptN27ZtI+EGAA9RXl6ksjJf+fo+pMaNWzvVFRdnKi9vngoLC+s8sfXUdgEAAM/n0Qn3gAEDdMcddygmJkbp6el6/PHHddNNN2n37t2yWq3Kzs6Wr6+vmjRp4rRdRESEsrOzJUnZ2dlmgv574eHhZkxVHA6HHA6HuV5YWFhLvQIAnI2fXzMFBl5Zqfx3/0uuF57aLgAA4Lk8OuEeMWKE+e/Y2Fh17txZMTExev/99zVs2LBqtzMMQxaLxVz//b+rizlTSkqKZs+e7WLLAQAAAACXugb1WLDIyEjFxMTo8OHDkiS73a6SkhLl5+c7xeXk5CgiIsKM+fnnnyvtKzc314ypyowZM1RQUGAumZmZtdgTAAAAAMDFrkEl3Hl5ecrMzFRkZKQkqVOnTvLx8VFaWpoZk5WVpf3796t79+6SpG7duqmgoECff/65GfPZZ5+poKDAjKmK1WpVcHCw0wIAAAAAwPmq14S7qKhI+/bt0759+yRJ6enp2rdvn44cOaKioiJNnz5d27dv1w8//KDNmzdryJAhCgsL06233ipJstlsGjdunKZNm6aPPvpIe/fu1Z133qm4uDhz1vK2bduqf//+mjBhgnbs2KEdO3ZowoQJGjx4MBOmAQBwFp988omGDBmiqKgoWSwWvfPOO071hmEoOTlZUVFR8vf3V3x8vA4cOOAU43A4NGXKFIWFhSkwMFBDhw7V0aNHnWLy8/OVmJgom80mm82mxMREHTt2zM29AwDA/eo14d61a5c6dOigDh06SJKmTp2qDh066IknnpCXl5e++uor3XzzzWrdurXGjBmj1q1ba/v27QoKCjL3sWDBAt1yyy0aPny4evTooYCAAL333nvmM7gladWqVYqLi1NCQoISEhJ09dVXa8WKFXXeXwAAGpITJ07ommuu0aJFi6qsnzt3rubPn69FixZp586dstvt6tu3r44fP27GJCUlad26dVqzZo22bt2qoqIiDR48WOXl5WbMqFGjtG/fPq1fv17r16/Xvn37lJiY6Pb+AQDgbvU6aVp8fLwMw6i2/sMPPzznPvz8/JSamqrU1NRqY0JCQrRy5UqX2ggAwKVqwIABGjBgQJV1hmFo4cKFmjlzpjmR6fLlyxUREaHVq1dr4sSJKigo0JIlS7RixQrzyrOVK1cqOjpaGzduVL9+/XTw4EGtX79eO3bsMB/h+eqrr6pbt246dOgQV6MBABq0BnUPNwAA8Azp6enKzs5WQkKCWWa1WtWzZ09t27ZNkrR7926VlpY6xURFRSk2NtaM2b59u2w2m5lsS1LXrl1ls9nMGAAAGiqPfiwYAACnlZY6lJGRUWVdRkaGysrK6rhFl7bs7GxJqvTEj4iICPN9ys7Olq+vr5o0aVIp5vT22dnZCg8Pr7T/8PBwM6YqDodDjt89BL2wsNC1jgAA4EYk3AAAj1dSkqeMjO81ZcozslqtleodjhPKzPxZNpujiq3hThaLxWndMIxKZWc6M6aq+HPtJyUlRbNnz65hawEAqFsk3AAAj1deXqSyMl/5+j6kxo1bV6rPz9+hsrKnVVZWXsXWcAe73S7ptzPUpx/XKUk5OTnmWW+73a6SkhLl5+c7neXOyckxH81pt9v1888/V9p/bm5upbPnvzdjxgxNnTrVXC8sLFR0dPSFdQoAgFrGPdwAgAbDz6+ZAgOvrLT4+UWee2PUqpYtW8putystLc0sKykp0ZYtW8xkulOnTvLx8XGKycrK0v79+82Ybt26qaCgQJ9//rkZ89lnn6mgoMCMqYrValVwcLDTAgCAp+EMNwAAqFJRUZG+/fZbcz09PV379u1TSEiImjdvrqSkJM2ZM0etWrVSq1atNGfOHAUEBGjUqFGSJJvNpnHjxmnatGkKDQ1VSEiIpk+frri4OHPW8rZt26p///6aMGGCXn75ZUnSPffco8GDBzNDOQCgwSPhBgAAVdq1a5d69eplrp++hHvMmDFatmyZHnnkERUXF2vSpEnKz89Xly5dtGHDBgUFBZnbLFiwQN7e3ho+fLiKi4vVu3dvLVu2TF5eXmbMqlWr9MADD5izmQ8dOrTaZ38DANCQkHADAIAqxcfHyzCMaustFouSk5OVnJxcbYyfn59SU1OVmppabUxISIhWrlx5IU0FAMAjcQ83AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG9Rrwv3JJ59oyJAhioqKksVi0TvvvGPWlZaW6i9/+Yvi4uIUGBioqKgo3XXXXfrpp5+c9hEfHy+LxeK0jBw50ikmPz9fiYmJstlsstlsSkxM1LFjx+qghwAAAACAS1W9JtwnTpzQNddco0WLFlWqO3nypPbs2aPHH39ce/bs0dtvv61vvvlGQ4cOrRQ7YcIEZWVlmcvLL7/sVD9q1Cjt27dP69ev1/r167Vv3z4lJia6rV8AAAAAAHjX54sPGDBAAwYMqLLOZrMpLS3NqSw1NVXXX3+9jhw5oubNm5vlAQEBstvtVe7n4MGDWr9+vXbs2KEuXbpIkl599VV169ZNhw4dUps2bWqpNwAAAAAA/FeDuoe7oKBAFotFjRs3dipftWqVwsLC1L59e02fPl3Hjx8367Zv3y6bzWYm25LUtWtX2Ww2bdu2ra6aDgAAAAC4xNTrGe6aOHXqlB599FGNGjVKwcHBZvno0aPVsmVL2e127d+/XzNmzNAXX3xhnh3Pzs5WeHh4pf2Fh4crOzu72tdzOBxyOBzmemFhYS32BgAAAABwsWsQCXdpaalGjhypiooKLV682KluwoQJ5r9jY2PVqlUrde7cWXv27FHHjh0lSRaLpdI+DcOosvy0lJQUzZ49u5Z6AAAAAAC41Hj8JeWlpaUaPny40tPTlZaW5nR2uyodO3aUj4+PDh8+LEmy2+36+eefK8Xl5uYqIiKi2v3MmDFDBQUF5pKZmXlhHQEAAAAAXFI8OuE+nWwfPnxYGzduVGho6Dm3OXDggEpLSxUZGSlJ6tatmwoKCvT555+bMZ999pkKCgrUvXv3avdjtVoVHBzstAAAAAAAcL7q9ZLyoqIiffvtt+Z6enq69u3bp5CQEEVFRen222/Xnj179K9//Uvl5eXmPdchISHy9fXVd999p1WrVmngwIEKCwvT119/rWnTpqlDhw7q0aOHJKlt27bq37+/JkyYYD4u7J577tHgwYOZoRwAAAAA4Db1mnDv2rVLvXr1MtenTp0qSRozZoySk5P17rvvSpKuvfZap+02bdqk+Ph4+fr66qOPPtLzzz+voqIiRUdHa9CgQZo1a5a8vLzM+FWrVumBBx5QQkKCJGno0KFVPvsbAAAAAIDaUq8Jd3x8vAzDqLb+bHWSFB0drS1btpzzdUJCQrRy5coatw8AAAAAAFd59D3cAAAAAAA0VCTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAHBJWVmZ/ud//kctW7aUv7+/rrjiCj355JOqqKgwYwzDUHJysqKiouTv76/4+HgdOHDAaT8Oh0NTpkxRWFiYAgMDNXToUB09erSuuwMAQK0j4QYAAC559tln9dJLL2nRokU6ePCg5s6dq+eee06pqalmzNy5czV//nwtWrRIO3fulN1uV9++fXX8+HEzJikpSevWrdOaNWu0detWFRUVafDgwSovL6+PbgEAUGu867sBAICay83NVWFhYZV1GRkZKisrq+MW4VK0fft23XzzzRo0aJAkqUWLFnrjjTe0a9cuSb+d3V64cKFmzpypYcOGSZKWL1+uiIgIrV69WhMnTlRBQYGWLFmiFStWqE+fPpKklStXKjo6Whs3blS/fv3qp3MAANQCEm4AaGByc3M1atR9ystzVFnvcJxQZubPstmqrgdqyw033KCXXnpJ33zzjVq3bq0vvvhCW7du1cKFCyVJ6enpys7OVkJCgrmN1WpVz549tW3bNk2cOFG7d+9WaWmpU0xUVJRiY2O1bds2Em4AQINGwg0ADUxhYaHy8hyyWqfJ3z+6Un1+/g6VlT2tsjIux4V7/eUvf1FBQYGuuuoqeXl5qby8XE8//bT+9Kc/SZKys7MlSREREU7bRUREKCMjw4zx9fVVkyZNKsWc3r4qDodDDsd/f1Sq7ooPAADqEwk3ADRQ/v7RCgy8slJ5cXFGPbQGl6K1a9dq5cqVWr16tdq3b699+/YpKSlJUVFRGjNmjBlnsVictjMMo1LZmc4Vk5KSotmzZ19YBwAAcDMmTQMAAC55+OGH9eijj2rkyJGKi4tTYmKiHnroIaWkpEiS7Ha7JFU6U52Tk2Oe9bbb7SopKVF+fn61MVWZMWOGCgoKzCUzM7M2uwYAQK0g4QYAAC45efKkGjVy/lPCy8vLfCxYy5YtZbfblZaWZtaXlJRoy5Yt6t69uySpU6dO8vHxcYrJysrS/v37zZiqWK1WBQcHOy0AAHgaLikHAAAuGTJkiJ5++mk1b95c7du31969ezV//nzdfffdkn67lDwpKUlz5sxRq1at1KpVK82ZM0cBAQEaNWqUJMlms2ncuHGaNm2aQkNDFRISounTpysuLs6ctRwAgIaKhBsAALgkNTVVjz/+uCZNmqScnBxFRUVp4sSJeuKJJ8yYRx55RMXFxZo0aZLy8/PVpUsXbdiwQUFBQWbMggUL5O3treHDh6u4uFi9e/fWsmXL5OXlVR/dAgCg1pBwAwAAlwQFBWnhwoXmY8CqYrFYlJycrOTk5Gpj/Pz8lJqaqtTU1NpvJAAA9Yh7uAEAAAAAcAMSbgAAAAAA3ICEGwAAAAAANyDhBgAAAADADUi4AQAAAABwA5cS7vT09NpuBwAAqCWM0wAAeAaXEu4//OEP6tWrl1auXKlTp07VdpsAAMAFYJwGAMAzuJRwf/HFF+rQoYOmTZsmu92uiRMn6vPPP6/ttgEAABcwTgMA4BlcSrhjY2M1f/58/fjjj1q6dKmys7N1ww03qH379po/f75yc3Nru50AAOA8MU4DAOAZLmjSNG9vb9166636xz/+oWeffVbfffedpk+frmbNmumuu+5SVlZWbbUTAADUEOM0AAD164IS7l27dmnSpEmKjIzU/PnzNX36dH333Xf6+OOP9eOPP+rmm2+urXYCAIAaYpwGAKB+ebuy0fz587V06VIdOnRIAwcO1Ouvv66BAweqUaPf8veWLVvq5Zdf1lVXXVWrjQUAAOfGOA0AgGdwKeF+8cUXdffdd+vPf/6z7HZ7lTHNmzfXkiVLLqhxAACg5hinAQDwDC4l3IcPHz5njK+vr8aMGePK7gEAwAVgnAYAwDO4dA/30qVL9eabb1Yqf/PNN7V8+fILbhQAAHAd4zQAAJ7BpYT7mWeeUVhYWKXy8PBwzZkz54IbBQAAXMc4DQCAZ3Ap4c7IyFDLli0rlcfExOjIkSMX3CgAAOA6xmkAADyDSwl3eHi4vvzyy0rlX3zxhUJDQy+4UQAAwHWM0wAAeAaXEu6RI0fqgQce0KZNm1ReXq7y8nJ9/PHHevDBBzVy5MjabiMAAKgBxmkAADyDS7OUP/XUU8rIyFDv3r3l7f3bLioqKnTXXXdxbxgAAPWMcRoAAM/gUsLt6+urtWvX6n//93/1xRdfyN/fX3FxcYqJiant9gEAgBpinAYAwDO4lHCf1rp1a7Vu3bq22gIAAGoR4zQAAPXLpYS7vLxcy5Yt00cffaScnBxVVFQ41X/88ce10jgAAFBzjNMAAHgGlxLuBx98UMuWLdOgQYMUGxsri8VS2+0CAAAuYpwGAMAzuJRwr1mzRv/4xz80cODA2m4PAAC4QIzTAAB4BpceC+br66s//OEPF/zin3zyiYYMGaKoqChZLBa98847TvWGYSg5OVlRUVHy9/dXfHy8Dhw44BTjcDg0ZcoUhYWFKTAwUEOHDtXRo0edYvLz85WYmCibzSabzabExEQdO3bsgtsPAIAnqq1xGgAAXBiXEu5p06bp+eefl2EYF/TiJ06c0DXXXKNFixZVWT937lzNnz9fixYt0s6dO2W329W3b18dP37cjElKStK6deu0Zs0abd26VUVFRRo8eLDKy8vNmFGjRmnfvn1av3691q9fr3379ikxMfGC2g4AgKeqrXEaAABcGJcuKd+6das2bdqkDz74QO3bt5ePj49T/dtvv31e+xkwYIAGDBhQZZ1hGFq4cKFmzpypYcOGSZKWL1+uiIgIrV69WhMnTlRBQYGWLFmiFStWqE+fPpKklStXKjo6Whs3blS/fv108OBBrV+/Xjt27FCXLl0kSa+++qq6deumQ4cOqU2bNq4cAgAAPFZtjdMAAODCuJRwN27cWLfeemttt8VJenq6srOzlZCQYJZZrVb17NlT27Zt08SJE7V7926VlpY6xURFRSk2Nlbbtm1Tv379tH37dtlsNjPZlqSuXbvKZrNp27ZtJNwAgItOXYzTAADg3FxKuJcuXVrb7agkOztbkhQREeFUHhERoYyMDDPG19dXTZo0qRRzevvs7GyFh4dX2n94eLgZUxWHwyGHw2GuFxYWutYRAADqWF2M0wAA4NxcuodbksrKyrRx40a9/PLL5j3VP/30k4qKimqtcZIqPcrEMIxzPt7kzJiq4s+1n5SUFHOSNZvNpujo6Bq2HACA+lNX4zQAAKieSwl3RkaG4uLidPPNN2vy5MnKzc2V9NskZ9OnT6+VhtntdkmqdBY6JyfHPOttt9tVUlKi/Pz8s8b8/PPPlfafm5tb6ez5782YMUMFBQXmkpmZeUH9AQCgrtTFOA0AAM7NpYT7wQcfVOfOnZWfny9/f3+z/NZbb9VHH31UKw1r2bKl7Ha70tLSzLKSkhJt2bJF3bt3lyR16tRJPj4+TjFZWVnav3+/GdOtWzcVFBTo888/N2M+++wzFRQUmDFVsVqtCg4OdloAAGgI6mKcBgAA5+byLOX/+c9/5Ovr61QeExOjH3/88bz3U1RUpG+//dZcT09P1759+xQSEqLmzZsrKSlJc+bMUatWrdSqVSvNmTNHAQEBGjVqlCTJZrNp3LhxmjZtmkJDQxUSEqLp06crLi7OnLW8bdu26t+/vyZMmKCXX35ZknTPPfdo8ODBTJgGALgo1dY4DQAALoxLCXdFRYXTc65PO3r0qIKCgs57P7t27VKvXr3M9alTp0qSxowZo2XLlumRRx5RcXGxJk2apPz8fHXp0kUbNmxweo0FCxbI29tbw4cPV3FxsXr37q1ly5bJy8vLjFm1apUeeOABczbzoUOHVvvsbwAAGrraGqcBAMCFcSnh7tu3rxYuXKhXXnlF0m+TkhUVFWnWrFkaOHDgee8nPj5ehmFUW2+xWJScnKzk5ORqY/z8/JSamqrU1NRqY0JCQrRy5crzbhcAAA1ZbY3TAADgwriUcC9YsEC9evVSu3btdOrUKY0aNUqHDx9WWFiY3njjjdpuIwAAqAHGaQAAPINLCXdUVJT27dunN954Q3v27FFFRYXGjRun0aNHO03OAgAA6h7jNAAAnsGlhFuS/P39dffdd+vuu++uzfYAAIBawDgNAED9cynhfv31189af9ddd7nUGAAAcOEYpwEA8AwuJdwPPvig03ppaalOnjwpX19fBQQEMJADAFCPGKcBAPAMjVzZKD8/32kpKirSoUOHdMMNNzAZCwAA9YxxGgAAz+BSwl2VVq1a6Zlnnqn0qzoAAKh/jNMAANS9Wku4JcnLy0s//fRTbe4SAADUEsZpAADqlkv3cL/77rtO64ZhKCsrS4sWLVKPHj1qpWEAAMA1jNMAAHgGlxLuW265xWndYrGoadOmuummmzRv3rzaaBcAAHAR4zQAAJ7BpYS7oqKittsBAABqCeM0AACeoVbv4QYAAAAAAL9x6Qz31KlTzzt2/vz5rrwEAABwEeM0AACewaWEe+/evdqzZ4/KysrUpk0bSdI333wjLy8vdezY0YyzWCy100oAAHDeGKcBAPAMLiXcQ4YMUVBQkJYvX64mTZpIkvLz8/XnP/9Zf/zjHzVt2rRabSQAADh/jNMAAHgGl+7hnjdvnlJSUsxBXJKaNGmip556itlPAQCoZ3U5Tv/444+68847FRoaqoCAAF177bXavXu3WW8YhpKTkxUVFSV/f3/Fx8frwIEDTvtwOByaMmWKwsLCFBgYqKFDh+ro0aO12k4AAOqDSwl3YWGhfv7550rlOTk5On78+AU3CgAAuK6uxun8/Hz16NFDPj4++uCDD/T1119r3rx5aty4sRkzd+5czZ8/X4sWLdLOnTtlt9vVt29fp3YkJSVp3bp1WrNmjbZu3aqioiINHjxY5eXltdZWAADqg0uXlN96663685//rHnz5qlr166SpB07dujhhx/WsGHDarWBAACgZupqnH722WcVHR2tpUuXmmUtWrQw/20YhhYuXKiZM2ear7t8+XJFRERo9erVmjhxogoKCrRkyRKtWLFCffr0kSStXLlS0dHR2rhxo/r161dr7QUAoK65dIb7pZde0qBBg3TnnXcqJiZGMTExGj16tAYMGKDFixfXdhsBAEAN1NU4/e6776pz58664447FB4erg4dOujVV18169PT05Wdna2EhASzzGq1qmfPntq2bZskaffu3SotLXWKiYqKUmxsrBkDAEBD5dIZ7oCAAC1evFjPPfecvvvuOxmGoT/84Q8KDAys7fYBAIAaqqtx+vvvv9eLL76oqVOn6rHHHtPnn3+uBx54QFarVXfddZeys7MlSREREU7bRUREKCMjQ5KUnZ0tX19fp/vNT8ec3r4qDodDDofDXC8sLKytbgEAUGtcOsN9WlZWlrKystS6dWsFBgbKMIzaahcAALhA7h6nKyoq1LFjR82ZM0cdOnTQxIkTNWHCBL344otOcWc+fswwjHM+kuxcMSkpKbLZbOYSHR3tekcAAHATlxLuvLw89e7dW61bt9bAgQOVlZUlSRo/fjyPGgEAoJ7V1TgdGRmpdu3aOZW1bdtWR44ckSTZ7XZJqnSmOicnxzzrbbfbVVJSovz8/GpjqjJjxgwVFBSYS2Zm5gX3BwCA2uZSwv3QQw/Jx8dHR44cUUBAgFk+YsQIrV+/vtYaBwAAaq6uxukePXro0KFDTmXffPONYmJiJEktW7aU3W5XWlqaWV9SUqItW7aoe/fukqROnTrJx8fHKSYrK0v79+83Y6pitVoVHBzstAAA4Glcuod7w4YN+vDDD9WsWTOn8latWpn3ZAEAgPpRV+P0Qw89pO7du2vOnDkaPny4Pv/8c73yyit65ZVXJP12KXlSUpLmzJmjVq1aqVWrVpozZ44CAgI0atQoSZLNZtO4ceM0bdo0hYaGKiQkRNOnT1dcXJw5azkAAA2VSwn3iRMnnH4xP+2XX36R1Wq94EYBAADX1dU4fd1112ndunWaMWOGnnzySbVs2VILFy7U6NGjzZhHHnlExcXFmjRpkvLz89WlSxdt2LBBQUFBZsyCBQvk7e2t4cOHq7i4WL1799ayZcvk5eVVa20FAKA+uHRJ+Y033qjXX3/dXLdYLKqoqNBzzz2nXr161VrjAABAzdXlOD148GB99dVXOnXqlA4ePKgJEyY41VssFiUnJysrK0unTp3Sli1bFBsb6xTj5+en1NRU5eXl6eTJk3rvvfeYBA0AcFFw6Qz3c889p/j4eO3atUslJSV65JFHdODAAf3666/6z3/+U9ttBAAANcA4DQCAZ3DpDHe7du305Zdf6vrrr1ffvn114sQJDRs2THv37tWVV15Z220EAAA1wDgNAIBnqPEZ7tLSUiUkJOjll1/W7Nmz3dEmAADgIsZpAAA8R43PcPv4+Gj//v2yWCzuaA8AALgAjNMAAHgOly4pv+uuu7RkyZLabgsAAKgFjNMAAHgGlyZNKykp0d///nelpaWpc+fOCgwMdKqfP39+rTQOAADUHOM0AACeoUYJ9/fff68WLVpo//796tixoyTpm2++cYrhEjYAAOoH4zQAAJ6lRgl3q1atlJWVpU2bNkmSRowYob/97W+KiIhwS+MAAMD5Y5wGAMCz1OgebsMwnNY/+OADnThxolYbBAAAXMM4DQCAZ3Fp0rTTzhzYAQCA52CcBgCgftUo4bZYLJXu/eJeMAAAPAPjNAAAnqVG93AbhqGxY8fKarVKkk6dOqV777230uynb7/9du21EAAAnBfGaQAAPEuNEu4xY8Y4rd9555212hgAAOA6xmkAADxLjRLupUuXuqsdAADgAjFOAwDgWS5o0jQAAAAAAFA1Em4AAAAAANzA4xPuFi1amLOu/n6ZPHmyJGns2LGV6rp27eq0D4fDoSlTpigsLEyBgYEaOnSojh49Wh/dAQAAAABcIjw+4d65c6eysrLMJS0tTZJ0xx13mDH9+/d3ivn3v//ttI+kpCStW7dOa9as0datW1VUVKTBgwervLy8TvsCAAAAALh01GjStPrQtGlTp/VnnnlGV155pXr27GmWWa1W2e32KrcvKCjQkiVLtGLFCvXp00eStHLlSkVHR2vjxo3q16+f+xoPAAAAALhkefwZ7t8rKSnRypUrdffdd8tisZjlmzdvVnh4uFq3bq0JEyYoJyfHrNu9e7dKS0uVkJBglkVFRSk2Nlbbtm2r0/YDAAAAAC4dHn+G+/feeecdHTt2TGPHjjXLBgwYoDvuuEMxMTFKT0/X448/rptuukm7d++W1WpVdna2fH191aRJE6d9RUREKDs7u9rXcjgccjgc5nphYWGt9wcAAAAAcPFqUAn3kiVLNGDAAEVFRZllI0aMMP8dGxurzp07KyYmRu+//76GDRtW7b4Mw3A6S36mlJQUzZ49u3YaDgAAAAC45DSYS8ozMjK0ceNGjR8//qxxkZGRiomJ0eHDhyVJdrtdJSUlys/Pd4rLyclRREREtfuZMWOGCgoKzCUzM/PCOwEAAAAAuGQ0mIR76dKlCg8P16BBg84al5eXp8zMTEVGRkqSOnXqJB8fH3N2c0nKysrS/v371b1792r3Y7VaFRwc7LQAAAAAAHC+GsQl5RUVFVq6dKnGjBkjb+//NrmoqEjJycm67bbbFBkZqR9++EGPPfaYwsLCdOutt0qSbDabxo0bp2nTpik0NFQhISGaPn264uLizFnLAQAAAACobQ0i4d64caOOHDmiu+++26ncy8tLX331lV5//XUdO3ZMkZGR6tWrl9auXaugoCAzbsGCBfL29tbw4cNVXFys3r17a9myZfLy8qrrrgDAecvNza1ywsaMjAyVlZXVQ4sAAABQEw0i4U5ISJBhGJXK/f399eGHH55zez8/P6Wmpio1NdUdzQOAWpebm6tRo+5TXp6jUp3DcUKZmT/LZqtcBwAAAM/RIBJuALjUFBYWKi/PIat1mvz9o53q8vN3qKzsaZWVlddT6wAAAHA+SLgBwIP5+0crMPBKp7Li4ox6ag0AAABqosHMUg4AAAAAQENCwg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAIBakZKSIovFoqSkJLPMMAwlJycrKipK/v7+io+P14EDB5y2czgcmjJlisLCwhQYGKihQ4fq6NGjddx6AABqHwk3AAC4YDt37tQrr7yiq6++2ql87ty5mj9/vhYtWqSdO3fKbrerb9++On78uBmTlJSkdevWac2aNdq6dauKioo0ePBglZeX13U3AACoVSTcAADgghQVFWn06NF69dVX1aRJE7PcMAwtXLhQM2fO1LBhwxQbG6vly5fr5MmTWr16tSSpoKBAS5Ys0bx589SnTx916NBBK1eu1FdffaWNGzfWV5cAAKgVJNwAAOCCTJ48WYMGDVKfPn2cytPT05Wdna2EhASzzGq1qmfPntq2bZskaffu3SotLXWKiYqKUmxsrBkDAEBD5V3fDQAAAA3XmjVrtGfPHu3cubNSXXZ2tiQpIiLCqTwiIkIZGRlmjK+vr9OZ8dMxp7evisPhkMPhMNcLCwtd7gMAAO7CGW4AAOCSzMxMPfjgg1q5cqX8/PyqjbNYLE7rhmFUKjvTuWJSUlJks9nMJTo6umaNBwCgDpBwAwAAl+zevVs5OTnq1KmTvL295e3trS1btuhvf/ubvL29zTPbZ56pzsnJMevsdrtKSkqUn59fbUxVZsyYoYKCAnPJzMys5d4BAHDhSLgBAIBLevfura+++kr79u0zl86dO2v06NHat2+frrjiCtntdqWlpZnblJSUaMuWLerevbskqVOnTvLx8XGKycrK0v79+82YqlitVgUHBzstAAB4Gu7hBgAALgkKClJsbKxTWWBgoEJDQ83ypKQkzZkzR61atVKrVq00Z84cBQQEaNSoUZIkm82mcePGadq0aQoNDVVISIimT5+uuLi4SpOwAQDQ0JBwAwAAt3nkkUdUXFysSZMmKT8/X126dNGGDRsUFBRkxixYsEDe3t4aPny4iouL1bt3by1btkxeXl712HIAAC4cCTcAAKg1mzdvdlq3WCxKTk5WcnJytdv4+fkpNTVVqamp7m0cAAB1jHu4AQAAAABwAxJuAAAAAADcgEvKAaCe5ObmqrCwsMq6jIwMlZWV1XGLAAAAUJtIuAGgHuTm5mrUqPuUl+eost7hOKHMzJ9ls1VdDwAAAM9Hwg0A9aCwsFB5eQ5ZrdPk7x9dqT4/f4fKyp5WWVl5PbQOAAAAtYGEGwDqkb9/tAIDr6xUXlycUQ+tAQAAQG1i0jQAAAAAANzAoxPu5ORkWSwWp8Vut5v1hmEoOTlZUVFR8vf3V3x8vA4cOOC0D4fDoSlTpigsLEyBgYEaOnSojh49WtddAQAAAABcYjw64Zak9u3bKysry1y++uors27u3LmaP3++Fi1apJ07d8put6tv3746fvy4GZOUlKR169ZpzZo12rp1q4qKijR48GCVl3NfJAAAAADAfTz+Hm5vb2+ns9qnGYahhQsXaubMmRo2bJgkafny5YqIiNDq1as1ceJEFRQUaMmSJVqxYoX69OkjSVq5cqWio6O1ceNG9evXr077AgAAAAC4dHj8Ge7Dhw8rKipKLVu21MiRI/X9999LktLT05Wdna2EhAQz1mq1qmfPntq2bZskaffu3SotLXWKiYqKUmxsrBkDAAAAAIA7ePQZ7i5duuj1119X69at9fPPP+upp55S9+7ddeDAAWVnZ0uSIiIinLaJiIhQRsZvs/tmZ2fL19dXTZo0qRRzevvqOBwOORz/ff5tYWFhbXQJAAAAAHCJ8OiEe8CAAea/4+Li1K1bN1155ZVavny5unbtKkmyWCxO2xiGUansTOcTk5KSotmzZ7vYcgAAAADApc7jLyn/vcDAQMXFxenw4cPmfd1nnqnOyckxz3rb7XaVlJQoPz+/2pjqzJgxQwUFBeaSmZlZiz0BAAAAAFzsGlTC7XA4dPDgQUVGRqply5ay2+1KS0sz60tKSrRlyxZ1795dktSpUyf5+Pg4xWRlZWn//v1mTHWsVquCg4OdFgAAAAAAzpdHX1I+ffp0DRkyRM2bN1dOTo6eeuopFRYWasyYMbJYLEpKStKcOXPUqlUrtWrVSnPmzFFAQIBGjRolSbLZbBo3bpymTZum0NBQhYSEaPr06YqLizNnLQcAAAAAwB08OuE+evSo/vSnP+mXX35R06ZN1bVrV+3YsUMxMTGSpEceeUTFxcWaNGmS8vPz1aVLF23YsEFBQUHmPhYsWCBvb28NHz5cxcXF6t27t5YtWyYvL6/66hYAAAAA4BLg0Qn3mjVrzlpvsViUnJys5OTkamP8/PyUmpqq1NTUWm4dAAAAAADVa1D3cAMAAAAA0FCQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG5Awg0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAACXpKSk6LrrrlNQUJDCw8N1yy236NChQ04xhmEoOTlZUVFR8vf3V3x8vA4cOOAU43A4NGXKFIWFhSkwMFBDhw7V0aNH67IrAAC4BQk3AABwyZYtWzR58mTt2LFDaWlpKisrU0JCgk6cOGHGzJ07V/Pnz9eiRYu0c+dO2e129e3bV8ePHzdjkpKStG7dOq1Zs0Zbt25VUVGRBg8erPLy8vroFgAAtca7vhsAAAAapvXr1zutL126VOHh4dq9e7duvPFGGYahhQsXaubMmRo2bJgkafny5YqIiNDq1as1ceJEFRQUaMmSJVqxYoX69OkjSVq5cqWio6O1ceNG9evXr877BQBAbeEMNwAAqBUFBQWSpJCQEElSenq6srOzlZCQYMZYrVb17NlT27ZtkyTt3r1bpaWlTjFRUVGKjY01YwAAaKg4ww0AAC6YYRiaOnWqbrjhBsXGxkqSsrOzJUkRERFOsREREcrIyDBjfH191aRJk0oxp7evisPhkMPhMNcLCwtrpR8AANQmznADAIALdv/99+vLL7/UG2+8UanOYrE4rRuGUansTOeKSUlJkc1mM5fo6GjXGg4AgBuRcAMAgAsyZcoUvfvuu9q0aZOaNWtmltvtdkmqdKY6JyfHPOttt9tVUlKi/Pz8amOqMmPGDBUUFJhLZmZmbXUHAIBaQ8INAABcYhiG7r//fr399tv6+OOP1bJlS6f6li1bym63Ky0tzSwrKSnRli1b1L17d0lSp06d5OPj4xSTlZWl/fv3mzFVsVqtCg4OdloAAPA03MMNAABcMnnyZK1evVr//Oc/FRQUZJ7Jttls8vf3l8ViUVJSkubMmaNWrVqpVatWmjNnjgICAjRq1Cgzdty4cZo2bZpCQ0MVEhKi6dOnKy4uzpy1HACAhsqjz3CnpKTouuuuU1BQkMLDw3XLLbfo0KFDTjFjx46VxWJxWrp27eoU43A4NGXKFIWFhSkwMFBDhw7V0aNH67IrAABcdF588UUVFBQoPj5ekZGR5rJ27Voz5pFHHlFSUpImTZqkzp0768cff9SGDRsUFBRkxixYsEC33HKLhg8frh49eiggIEDvvfeevLy86qNbAADUGo9OuLds2aLJkydrx44dSktLU1lZmRISEnTixAmnuP79+ysrK8tc/v3vfzvVJyUlad26dVqzZo22bt2qoqIiDR48WOXl5XXZHQAALiqGYVS5jB071oyxWCxKTk5WVlaWTp06pS1btpizmJ/m5+en1NRU5eXl6eTJk3rvvfeYBA0AcFHw6EvK169f77S+dOlShYeHa/fu3brxxhvNcqvVak7McqaCggItWbJEK1asMC9NW7lypaKjo7Vx40b169fPfR0AAAAAAFyyPPoM95kKCgokSSEhIU7lmzdvVnh4uFq3bq0JEyYoJyfHrNu9e7dKS0uVkJBglkVFRSk2Nlbbtm2rm4YDAAAAAC45Hn2G+/cMw9DUqVN1ww03OF2KNmDAAN1xxx2KiYlRenq6Hn/8cd10003avXu3rFarsrOz5evrqyZNmjjtLyIiotJjSn7P4XDI4XCY64WFhbXfKQAAAADARavBJNz333+/vvzyS23dutWpfMSIEea/Y2Nj1blzZ8XExOj999/XsGHDqt2fYRiyWCzV1qekpGj27NkX3nAAAAAAwCWpQVxSPmXKFL377rvatGmTmjVrdtbYyMhIxcTE6PDhw5Iku92ukpIS5efnO8Xl5OQoIiKi2v3MmDFDBQUF5pKZmXnhHQEAAAAAXDI8+gy3YRiaMmWK1q1bp82bN6tly5bn3CYvL0+ZmZmKjIyUJHXq1Ek+Pj5KS0vT8OHDJUlZWVnav3+/5s6dW+1+rFarrFZr7XQEwCUrNze3yltSMjIyVFZWVg8tAgAAQF3x6IR78uTJWr16tf75z38qKCjIvOfaZrPJ399fRUVFSk5O1m233abIyEj98MMPeuyxxxQWFqZbb73VjB03bpymTZum0NBQhYSEaPr06YqLizNnLQcAd8jNzdWoUfcpL89Rqc7hOKHMzJ9ls1WuAwAAwMXBoxPuF198UZIUHx/vVL506VKNHTtWXl5e+uqrr/T666/r2LFjioyMVK9evbR27VoFBQWZ8QsWLJC3t7eGDx+u4uJi9e7dW8uWLZOXl1dddgfAJaawsFB5eQ5ZrdPk7+/8TOH8/B0qK3taZWXl9dQ6AAAAuJtHJ9yGYZy13t/fXx9++OE59+Pn56fU1FSlpqbWVtMA4Lz5+0crMPBKp7Li4ox6ag0AAADqSoOYNA0AAAAAgIaGhBsAAAAAADcg4QYAAAAAwA1IuAEAAAAAcAMSbgAAAAAA3ICEGwAAAAAANyDhBgAAAADADUi4AQAAAABwAxJuAAAAAADcgIQbAAAAAAA3IOEGAAAAAMANSLgBAAAAAHADEm4AAAAAANyAhBsAAAAAADcg4QYAAAAAwA1IuAEAAAAAcAMSbgAAAAAA3ICEGwAAAAAANyDhBgAAAADADUi4AQAAAABwAxJuAAAAAADcgIQbAAAAAAA3IOEGAAAAAMANSLgBAAAAAHADEm4AAAAAANyAhBsAAAAAADcg4QYAAAAAwA1IuAEAAAAAcAMSbgAAAAAA3ICEGwAAAAAANyDhBgAAAADADUi4AQAAAABwAxJuAAAAAADcgIQbAAAAAAA3IOEGAAAAAMANSLgBAAAAAHADEm4AAAAAANyAhBsAAAAAADcg4QYAAAAAwA2867sBANCQ5ebmqrCwsMq6jIwMlZWV1XGLAAAA4ClIuAHARbm5uRo16j7l5TmqrHc4Tigz82fZbFXXA0BNlJY6lJGRUd/NcBIcHKymTZvWdzMAwGORcAOAiwoLC5WX55DVOk3+/tGV6vPzd6is7GmVlZXXQ+sAXExKSvKUkfG9pkx5Rlartb6bYwoNtWr16hdJugGgGpdUwr148WI999xzysrKUvv27bVw4UL98Y9/rO9mAWjg/P2jFRh4ZaXy4mLPOhMFeDrG6eqVlxeprMxXvr4PqXHj1vXdHElScXGm8vLmqbCwkIQbAKpxySTca9euVVJSkhYvXqwePXro5Zdf1oABA/T111+refPm9d08AAAuaYzT58fPr1mVP/DVFwd3zADAWV0yCff8+fM1btw4jR8/XpK0cOFCffjhh3rxxReVkpJSz60DcC5nm5zsXPcQnm1bSSopKZGvr2+N65gUDag9jNMNE/eVA8DZXRIJd0lJiXbv3q1HH33UqTwhIUHbtm2rp1YBOF/nmpwsKEh67rnHFRoaWqkuLy9PDz/8lI4fN6rctrTUoZ9+Stfll/9B3t7e510nMSkaUFsYpxsm7iuvmXP9+Fsf+HECtY3PeWWXRML9yy+/qLy8XBEREU7lERERys7OrnIbh8Mhx++ukyooKJCkWvkA/frrrzp27NgF7we4VGRmZuqnnwrk5TVcvr5hTnUnT36vw4df0F13PSqrtfKZaIfjpH78MUfR0Q8oICCyUn1Jydc6eTJDDscg+fpeft51klRW9rVKS19XQcFBWSwllepPnPhOhlGuEye+kY9P+XnXuXNb2lW77Sou/lHl5aU6fvz4BY8Pp7c3jKp/HLqYedI4ffz48f/3nv6fysqOX9C+atO5Pqf1obDwC5WWeqmsbKj8/Sv/P7I+lJT8op9+WqMdO3YoOrryZJb15ddff9UTT/xVx49X1HdTnAQFWfTkkw8rJCSkvpuCi4Cnfs5DQqxasmSBwsLCzh18Fi6P08Yl4McffzQkGdu2bXMqf+qpp4w2bdpUuc2sWbMMSSwsLCwsLHW6ZGZm1sXQ6FEYp1lYWFhYGspS03H6kjjDHRYWJi8vr0q/kufk5FT6Nf20GTNmaOrUqeZ6RUWFfv31V4WGhspisbjclsLCQkVHRyszM1PBwcEu7+dSwjGrGY5XzXC8aobjVTM1OV6GYej48eOKioqqo9Z5DsbpunGx9o1+NSz0q+G5WPtW0365Ok5fEgm3r6+vOnXqpLS0NN16661meVpamm6++eYqt7FarZXuR2rcuHGttSk4OPii+sDWBY5ZzXC8aobjVTMcr5o53+Nls9nqoDWeh3G6bl2sfaNfDQv9angu1r7VpF+ujNOXRMItSVOnTlViYqI6d+6sbt266ZVXXtGRI0d077331nfTAAC45DFOAwAuRpdMwj1ixAjl5eXpySefVFZWlmJjY/Xvf/9bMTEx9d00AAAueYzTAICL0SWTcEvSpEmTNGnSpHptg9Vq1axZszzq8RmejmNWMxyvmuF41QzHq2Y4XjXDOO1eF2vf6FfDQr8anou1b3XVL4thXILPHwEAAAAAwM0a1XcDAAAAAAC4GJFwAwAAAADgBiTcAAAAAAC4AQm3m3zyyScaMmSIoqKiZLFY9M477zjVG4ah5ORkRUVFyd/fX/Hx8Tpw4ED9NNYDpKSk6LrrrlNQUJDCw8N1yy236NChQ04xHLP/evHFF3X11Vebzw3s1q2bPvjgA7OeY3V2KSkpslgsSkpKMss4Zv+VnJwsi8XitNjtdrOeY1XZjz/+qDvvvFOhoaEKCAjQtddeq927d5v1HLP6tXjxYrVs2VJ+fn7q1KmTPv3007PGb9myRZ06dZKfn5+uuOIKvfTSS5Vi3nrrLbVr105Wq1Xt2rXTunXr3NX8atWkX2+//bb69u2rpk2bmuPGhx9+6BSzbNmySt99i8WiU6dOubsrTmrSr82bN1fZ5v/7v/9zimto79fYsWOr7Ff79u3NGE94v871925VGsL3q6b9akjfr5r2raF8x2rar7r8jpFwu8mJEyd0zTXXaNGiRVXWz507V/Pnz9eiRYu0c+dO2e129e3bV8ePH6/jlnqGLVu2aPLkydqxY4fS0tJUVlamhIQEnThxwozhmP1Xs2bN9Mwzz2jXrl3atWuXbrrpJt18883mH/Acq+rt3LlTr7zyiq6++mqnco6Zs/bt2ysrK8tcvvrqK7OOY+UsPz9fPXr0kI+Pjz744AN9/fXXmjdvnho3bmzGcMzqz9q1a5WUlKSZM2dq7969+uMf/6gBAwboyJEjVcanp6dr4MCB+uMf/6i9e/fqscce0wMPPKC33nrLjNm+fbtGjBihxMREffHFF0pMTNTw4cP12Wef1VW3atyvTz75RH379tW///1v7d69W7169dKQIUO0d+9ep7jg4GCn735WVpb8/PzqokuSat6v0w4dOuTU5latWpl1DfH9ev755536k5mZqZCQEN1xxx1OcfX9fp3r790zNZTvV0371VC+X1LN+3aap3/HatqvOv2OGXA7Sca6devM9YqKCsNutxvPPPOMWXbq1CnDZrMZL730Uj200PPk5OQYkowtW7YYhsExOx9NmjQx/v73v3OszuL48eNGq1atjLS0NKNnz57Ggw8+aBgGn68zzZo1y7jmmmuqrONYVfaXv/zFuOGGG6qt55jVr+uvv9649957ncquuuoq49FHH60y/pFHHjGuuuoqp7KJEycaXbt2NdeHDx9u9O/f3ymmX79+xsiRI2up1edW035VpV27dsbs2bPN9aVLlxo2m622muiSmvZr06ZNhiQjPz+/2n1eDO/XunXrDIvFYvzwww9mmSe8X7935t+7VWko36/fO59+VcUTv19nOp++NZTv2O+58p658zvGGe56kJ6eruzsbCUkJJhlVqtVPXv21LZt2+qxZZ6joKBAkhQSEiKJY3Y25eXlWrNmjU6cOKFu3bpxrM5i8uTJGjRokPr06eNUzjGr7PDhw4qKilLLli01cuRIff/995I4VlV599131blzZ91xxx0KDw9Xhw4d9Oqrr5r1HLP6U1JSot27dzsde0lKSEio9thv3769Uny/fv20a9culZaWnjWmrt5PV/p1poqKCh0/ftwcZ08rKipSTEyMmjVrpsGDB1c6Q+dOF9KvDh06KDIyUr1799amTZuc6i6G92vJkiXq06ePYmJinMrr8/1yRUP4ftUGT/x+XShP/o7VBnd+x0i460F2drYkKSIiwqk8IiLCrLuUGYahqVOn6oYbblBsbKwkjllVvvrqK1122WWyWq269957tW7dOrVr145jVY01a9Zoz549SklJqVTHMXPWpUsXvf766/rwww/16quvKjs7W927d1deXh7Hqgrff/+9XnzxRbVq1Uoffvih7r33Xj3wwAN6/fXXJfH5qk+//PKLysvLa3Tss7Ozq4wvKyvTL7/8ctaYuno/XenXmebNm6cTJ05o+PDhZtlVV12lZcuW6d1339Ubb7whPz8/9ejRQ4cPH67V9lfHlX5FRkbqlVde0VtvvaW3335bbdq0Ue/evfXJJ5+YMQ39/crKytIHH3yg8ePHO5XX9/vliobw/aoNnvj9clVD+I5dKHd/x7xrs7GoGYvF4rRuGEalskvR/fffry+//FJbt26tVMcx+682bdpo3759OnbsmN566y2NGTNGW7ZsMes5Vv+VmZmpBx98UBs2bDjrfTccs98MGDDA/HdcXJy6deumK6+8UsuXL1fXrl0lcax+r6KiQp07d9acOXMk/XYW4MCBA3rxxRd11113mXEcs/pT02NfVfyZ5Z7wfrrahjfeeEPJycn65z//qfDwcLO8a9eu5ndcknr06KGOHTsqNTVVf/vb32qv4edQk361adNGbdq0Mde7deumzMxM/fWvf9WNN97o0j7dxdU2LFu2TI0bN9Ytt9ziVO4p71dNNZTvl6s8/ftVUw3pO+Yqd3/HOMNdD07P9nvmrz45OTmVfh261EyZMkXvvvuuNm3apGbNmpnlHLPKfH199Yc//EGdO3dWSkqKrrnmGj3//PMcqyrs3r1bOTk56tSpk7y9veXt7a0tW7bob3/7m7y9vc3jwjGrWmBgoOLi4nT48GE+X1WIjIxUu3btnMratm1rTobEMas/YWFh8vLyqtGxt9vtVcZ7e3srNDT0rDF19X660q/T1q5dq3Hjxukf//hHpdtrztSoUSNdd911dXYG7kL69Xtdu3Z1anNDfr8Mw9Brr72mxMRE+fr6njW2rt8vVzSE79eF8OTvV23ytO/YhaiL7xgJdz1o2bKl7Ha70tLSzLKSkhJt2bJF3bt3r8eW1R/DMHT//ffr7bff1scff6yWLVs61XPMzs0wDDkcDo5VFXr37q2vvvpK+/btM5fOnTtr9OjR2rdvn6644gqO2Vk4HA4dPHhQkZGRfL6q0KNHj0qPMfzmm2/M+8A4ZvXH19dXnTp1cjr2kpSWllbtse/WrVul+A0bNqhz587y8fE5a0xdvZ+u9Ev67czb2LFjtXr1ag0aNOicr2MYhvbt26fIyMgLbvP5cLVfZ9q7d69Tmxvq+yX99hSXb7/9VuPGjTvn69T1++WKhvD9cpWnf79qk6d9xy5EnXzHLnjaNVTp+PHjxt69e429e/cakoz58+cbe/fuNTIyMgzDMIxnnnnGsNlsxttvv2189dVXxp/+9CcjMjLSKCwsrOeW14/77rvPsNlsxubNm42srCxzOXnypBnDMfuvGTNmGJ988omRnp5ufPnll8Zjjz1mNGrUyNiwYYNhGByr8/H7WcoNg2P2e9OmTTM2b95sfP/998aOHTuMwYMHG0FBQebMnRwrZ59//rnh7e1tPP3008bhw4eNVatWGQEBAcbKlSvNGI5Z/VmzZo3h4+NjLFmyxPj666+NpKQkIzAw0Pw8P/roo0ZiYqIZ//333xsBAQHGQw89ZHz99dfGkiVLDB8fH+P/+//+PzPmP//5j+Hl5WU888wzxsGDB41nnnnG8Pb2Nnbs2OGx/Vq9erXh7e1tvPDCC07j7LFjx8yY5ORkY/369cZ3331n7N271/jzn/9seHt7G5999pnH9mvBggXGunXrjG+++cbYv3+/8eijjxqSjLfeesuMaYjv12l33nmn0aVLlyr36Qnv17n+3m2o36+a9quhfL9c6VtD+Y7VtF+n1cV3jITbTU5PoX/mMmbMGMMwfntMzKxZswy73W5YrVbjxhtvNL766qv6bXQ9qupYSTKWLl1qxnDM/uvuu+82YmJiDF9fX6Np06ZG7969zWTbMDhW5+PMhJtj9l8jRowwIiMjDR8fHyMqKsoYNmyYceDAAbOeY1XZe++9Z8TGxhpWq9W46qqrjFdeecWpnmNWv1544QXz/5kdO3Y0HzlpGIYxZswYo2fPnk7xmzdvNjp06GD4+voaLVq0MF588cVK+3zzzTeNNm3aGD4+PsZVV13l9MdnXalJv3r27HnWv0sMwzCSkpKM5s2bm2NLQkKCsW3btjrs0W9q0q9nn33WuPLKKw0/Pz+jSZMmxg033GC8//77lfbZ0N4vwzCMY8eOGf7+/pX+f3KaJ7xf5/p7t6F+v2rar4b0/app3xrKd8yVz2JdfccshvH/ZioAAAAAAAC1hnu4AQAAAABwAxJuAAAAAADcgIQbAAAAAAA3IOEGAAAAAMANSLgBAAAAAHADEm4AAAAAANyAhBsAAAAAADcg4QYAAAAAwA1IuAHUuvj4eCUlJdV3MwAAQBUYp4G6Q8INwMmQIUPUp0+fKuu2b98ui8WiPXv21HGrAACAxDgNNDQk3ACcjBs3Th9//LEyMjIq1b322mu69tpr1bFjx3poGQAAYJwGGhYSbgBOBg8erPDwcC1btsyp/OTJk1q7dq1uueUW/elPf1KzZs0UEBCguLg4vfHGG2fdp8Vi0TvvvONU1rhxY6fX+PHHHzVixAg1adJEoaGhuvnmm/XDDz/UTqcAALhIME4DDQsJNwAn3t7euuuuu7Rs2TIZhmGWv/nmmyopKdH48ePVqVMn/etf/9L+/ft1zz33KDExUZ999pnLr3ny5En16tVLl112mT755BNt3bpVl112mfr376+SkpLa6BYAABcFxmmgYSHhBlDJ3XffrR9++EGbN282y1577TUNGzZMl19+uaZPn65rr71WV1xxhaZMmaJ+/frpzTffdPn11qxZo0aNGunvf/+74uLi1LZtWy1dulRHjhxxagMAAGCcBhoS7/puAADPc9VVV6l79+567bXX1KtXL3333Xf69NNPtWHDBpWXl+uZZ57R2rVr9eOPP8rhcMjhcCgwMNDl19u9e7e+/fZbBQUFOZWfOnVK33333YV2BwCAiwrjNNBwkHADqNK4ceN0//3364UXXtDSpUsVExOj3r1767nnntOCBQu0cOFCxcXFKTAwUElJSWe9pMxisThd9iZJpaWl5r8rKirUqVMnrVq1qtK2TZs2rb1OAQBwkWCcBhoGEm4AVRo+fLgefPBBrV69WsuXL9eECRNksVj06aef6uabb9add94p6bdB+PDhw2rbtm21+2ratKmysrLM9cOHD+vkyZPmeseOHbV27VqFh4crODjYfZ0CAOAiwTgNNAzcww2gSpdddplGjBihxx57TD/99JPGjh0rSfrDH/6gtLQ0bdu2TQcPHtTEiROVnZ191n3ddNNNWrRokfbs2aNdu3bp3nvvlY+Pj1k/evRohYWF6eabb9ann36q9PR0bdmyRQ8++KCOHj3qzm4CANAgMU4DDQMJN4BqjRs3Tvn5+erTp4+aN28uSXr88cfVsWNH9evXT/Hx8bLb7brlllvOup958+YpOjpaN954o0aNGqXp06crICDArA8ICNAnn3yi5s2ba9iwYWrbtq3uvvtuFRcX80s6AADVYJwGPJ/FOPOGDQAAAAAAcME4ww0AAAAAgBuQcAMAAAAA4AYk3AAAAAAAuAEJNwAAAAAAbkDCDQAAAACAG5BwAwAAAADgBiTcAAAAAAC4AQk3AAAAAABuQMINAAAAAIAbkHADAAAAAOAGJNwAAAAAALgBCTcAAAAAAG7w/wMEI02LzTu23QAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(10, 5))\n", "\n", "duration = np_off_time - np_on_time\n", "axes[0].hist(duration, bins=np.arange(10, 60, 1), color='blue', alpha=0.7, edgecolor='black')\n", "axes[0].set_title('Histogram of Duration')\n", "axes[0].set_xlabel('Value')\n", "axes[0].set_ylabel('Frequency')\n", "\n", "np_img = np_img_all\n", "np_spike = np_spike_all\n", "\n", "np_spike_mean = np_spike.mean(axis=0)\n", "axes[1].hist(np_spike_mean, bins=10, color='blue', alpha=0.7, edgecolor='black')\n", "axes[1].set_title('Histogram of Spiking Rate')\n", "axes[1].set_xlabel('Value')\n", "axes[1].set_ylabel('Frequency')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 14, "id": "danish-export", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.84577780e+02 2.09629251e+02 2.34623919e+02 ... 6.00626272e+05\n", " 6.00651280e+05 6.00676435e+05] [2.05577780e+02 2.30629251e+02 2.55623919e+02 ... 6.00648008e+05\n", " 6.00680204e+05 6.00697435e+05]\n"]}], "source": ["import h5py\n", "import numpy as np\n", "origin_neu_file = 'PLCAT009_Ephys_d2_Image_0.mat'\n", "with h5py.File(f'./{origin_neu_file}', 'r') as mat_file:\n", "    np_on_time = np.array([mat_file[i][0, 0] for i in mat_file['ex']['CondTest']['Command_COND'][:, 0]])\n", "    np_on2_time = mat_file['ex']['CondTest']['CondOn'][:, 0]\n", "print(np_on_time, np_on2_time)"]}, {"cell_type": "code", "execution_count": 4, "id": "foster-alexander", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 24000/24000 [00:08<00:00, 2730.08it/s]\n"]}], "source": ["preprocess(lag=0, bin_size=15, bin_cnt=6)"]}, {"cell_type": "code", "execution_count": 5, "id": "passing-palestinian", "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "with open('./data.pkl', 'rb') as f:\n", "    np_img = pickle.load(f)\n", "    np_spike_cnt = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "better-tennis", "metadata": {}, "outputs": [], "source": ["indices = np.arange(24000)\n", "np.random.shuffle(indices)\n", "indices"]}, {"cell_type": "code", "execution_count": null, "id": "angry-kennedy", "metadata": {}, "outputs": [], "source": ["np_img = np_img[indices]\n", "np_spike_cnt = np_spike_cnt[indices]"]}, {"cell_type": "code", "execution_count": null, "id": "meaningful-madonna", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "np_img = np.transpose(np_img, [1, 2, 0])\n", "print(np_img.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "stable-rover", "metadata": {}, "outputs": [], "source": ["from scipy.ndimage import zoom\n", "np_img_2 = np.zeros([30, 30, 24000])\n", "\n", "for i in range(24000):\n", "    downsample = zoom(np_img[:, :, i], 30/64, order=3)\n", "    np_img_2[:, :, i] = downsample"]}, {"cell_type": "code", "execution_count": null, "id": "possible-ontario", "metadata": {}, "outputs": [], "source": ["np_img_2.shape\n", "np_img = np_img_2"]}, {"cell_type": "code", "execution_count": null, "id": "entertaining-headquarters", "metadata": {}, "outputs": [], "source": ["data = {\n", "    'Training_stimuli': np_img[:, :, :16800],\n", "    'Validation_stimuli': np_img[:, :, 16800:19200],\n", "    'Testing_stimuli':  np_img[:, :, 19200:]\n", "}\n", "\n", "\n", "sio.savemat('DataSets_ZHJ2024_64.mat', data)\n", "\n", "print(\"保存成功：data.mat 文件已生成\")"]}, {"cell_type": "code", "execution_count": null, "id": "romance-vacuum", "metadata": {}, "outputs": [], "source": ["data = {\n", "    'est_resp': np_spike_cnt[:16800, 0, :, :],\n", "    'reg_resp': np_spike_cnt[16800:19200, 0, :, :],\n", "    'pred_resp':  np_spike_cnt[19200:, 0, :, :]\n", "}\n", "\n", "\n", "sio.savemat('DataSets_ZHJ2024_Resp.mat', data)"]}, {"cell_type": "code", "execution_count": 18, "id": "light-facial", "metadata": {}, "outputs": [{"data": {"text/plain": ["(305,)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["b=np_spike_cnt[:, 0, :, :].mean(axis=0).mean(axis=0)\n", "b.shape"]}, {"cell_type": "code", "execution_count": 20, "id": "congressional-oasis", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 37, 161,  20,  88, 269,   3, 131, 127, 168,  78, 113, 109, 156,\n", "       128, 197, 149,  13, 111, 143,  98, 120, 121,  72, 209,  75, 249,\n", "       215, 122, 230,  27,  90,  69, 182, 137, 190, 224, 164,  56,  59,\n", "       206,  16,  89, 238, 205, 159,  87, 235, 208, 288,  95, 155, 298,\n", "       178, 303, 301, 142,  33, 279,  54, 214, 273, 181, 199, 292, 180,\n", "        53, 291, 107, 191, 289, 295, 176,   6, 217,  24,  80, 204, 232,\n", "         4, 302, 294, 252,  58,  55, 153, 241, 270,  94, 278, 166, 284,\n", "       189, 184, 115, 116, 193,  77,  68, 274, 187, 104, 160, 183, 246,\n", "        86, 173, 231, 225, 253, 275, 201, 259, 293, 242, 237, 154, 152,\n", "       261, 202,  52, 133,  65,  34,  57, 267,  11, 118, 198, 219, 220,\n", "       136, 250,  71, 175, 102,  93, 263, 247,  62, 177,   9, 227, 254,\n", "       221, 207, 256, 304, 268,  73,  85, 282,  79, 251,  19, 195, 287,\n", "       130,  44,  92, 213, 228, 169, 255, 151,  81, 243, 276, 280, 216,\n", "        67, 236,  83,  50, 258, 170,  23, 281,  39, 300,  70, 296, 119,\n", "        97,  96, 290, 108, 223,  36, 244, 260, 144,  82, 141,  63, 157,\n", "        38, 297,  29, 101, 264, 147,  99, 140,  35,  17, 233, 110, 171,\n", "       172,  49, 185, 248, 179, 112, 239, 138, 222,  51, 245,  32, 158,\n", "        74, 186,  84, 123, 218, 148, 203,  31,  91, 200,  14, 211,  64,\n", "       234, 210, 286, 299, 129, 126, 135,   1, 145, 163, 188, 262, 196,\n", "       103,  47, 124, 105, 146,  45, 134,  48,  76,  15,  25, 125,   8,\n", "       150,  41, 229,  42, 106, 277,   2,  66,  18, 167, 132, 165,  22,\n", "        40,  43, 117, 194, 192,  61, 100, 212,  12, 174,  60,  26,   0,\n", "        30,  10, 139,  28,  46, 240, 162, 272,   7, 266, 257, 285,   5,\n", "        21, 265, 226, 283, 114, 271], dtype=int64)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["b.a<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 17, "id": "artificial-boston", "metadata": {}, "outputs": [], "source": ["b.sort()"]}, {"cell_type": "code", "execution_count": 13, "id": "reverse-superintendent", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████████████████████████████████| 72000/72000 [00:00<00:00, 166908.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["(72000, 20)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import os\n", "import math\n", "import h5py\n", "import pickle\n", "import argparse\n", "import numpy as np\n", "from tqdm import tqdm\n", "from scipy.ndimage import zoom\n", "import scipy.io as sio\n", "\n", "origin_neu_file = 'PLCATD001_Ephys_DA07_Image_0.mat'\n", "origin_img_file = 'Lum_n24000_sizedeg(3, 3)_sizepx(64, 64)_pc(5, 95)_sd(0.2, 0.35).mat'\n", "\n", "with h5py.File(f'./{origin_neu_file}', 'r') as mat_file:\n", "    np_on_time = mat_file['ex']['CondTest']['CondOn'][:, 0]\n", "    np_off_time = mat_file['ex']['CondTest']['CondOff'][:, 0]\n", "    np_img_id = mat_file['ex']['CondTest']['CondIndex'][:, 0] - 1\n", "    np_spiking_cluster_id = mat_file['spike0_kilosort3']['cluster'][0]\n", "    np_spiking_time = mat_file['spike0_kilosort3']['time'][0]\n", "    map_cluster_id = {v: i for i, v in enumerate(mat_file['spike0_kilosort3']['clusterid'][:][0])}\n", "\n", "for i, v in map_cluster_id.items():\n", "    if v == 285:\n", "        np_spiking_time = np_spiking_time[np_spiking_cluster_id == i]\n", "        break\n", "\n", "        \n", "np_spike_cnt = []\n", "left, right = 0, 0\n", "\n", "for i in tqdm(np_off_time):\n", "    while right < len(np_spiking_time) and np_spiking_time[right] <= i:\n", "        right += 1\n", "    while left < right and np_spiking_time[left] < i-80:\n", "        left += 1\n", "        \n", "    np_spike_cnt.append([right-left]*20)\n", "np_spike_cnt = np.array(np_spike_cnt)\n", "print(np_spike_cnt.shape)\n", "\n", "sio.savemat('PLCATD001_Ephys_DA07_Image_estSetResp.mat', {'est_resp': np_spike_cnt[:16800, :5]})\n", "sio.savemat('PLCATD001_Ephys_DA07_Image_regSetResp.mat', {'reg_resp': np_spike_cnt[16800:19200, :20]})\n", "sio.savemat('PLCATD001_Ephys_DA07_Image_predSetResp.mat', {'pred_resp': np_spike_cnt[19200:, :20]})"]}, {"cell_type": "code", "execution_count": 14, "id": "julian-thesaurus", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████████████████████████████████████████████████████████████████████| 24000/24000 [00:11<00:00, 2146.67it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["(30, 30, 72000)\n"]}], "source": ["with h5py.File(f'./{origin_img_file}', 'r') as mat_file:\n", "    np_img = np.array([\n", "        zoom(mat_file[i][:, :], 30/64, order=3) \n", "        for i in tqdm(mat_file['imgset'])\n", "    ])\n", "    \n", "np_img = np_img[np_img_id]\n", "np_img = np.transpose(np_img, [1, 2, 0])\n", "print(np_img.shape)\n", "\n", "data = {\n", "    'Training_stimuli': np_img[:, :, :16800],\n", "    'Validation_stimuli': np_img[:, :, 16800:19200],\n", "    'Testing_stimuli':  np_img[:, :, 19200:]\n", "}\n", "\n", "\n", "sio.savemat('DataSets_PLCATD001_Ephys_DA07_Image_0.mat', data)"]}, {"cell_type": "code", "execution_count": null, "id": "divine-caribbean", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "excess-fundamentals", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "rfest", "language": "python", "name": "rfest"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}