#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试坐标系修复
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加支持文件路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SysIden_AvgMethod_SupportFiles'))
from SysIden_AvgMethod_SupportFiles.k_functions import plotReconstruction

def create_clear_directional_pattern():
    """创建一个明确的方向性模式"""
    filterSize = 5
    numLags = 1
    
    # 创建一个明确的"F"形状
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    
    # F形状：
    # ■■■■
    # ■
    # ■■■
    # ■
    # ■
    
    filterWeights[0, 0:4, 0] = 1.0  # 顶部横线
    filterWeights[1, 0, 0] = 1.0    # 左侧竖线
    filterWeights[2, 0:3, 0] = 1.0  # 中间横线
    filterWeights[3, 0, 0] = 1.0    # 左侧竖线
    filterWeights[4, 0, 0] = 1.0    # 左侧竖线
    
    return filterWeights

def test_detailed_reconstruction():
    """详细测试重构过程"""
    print("=" * 60)
    print("详细测试RF重构坐标系")
    print("=" * 60)
    
    # 创建F形滤波器
    filterWeights = create_clear_directional_pattern()
    
    print("原始F形滤波器（应该显示为F）:")
    print("期望形状:")
    print("■■■■")
    print("■")
    print("■■■")
    print("■")
    print("■")
    print()
    
    # 显示原始滤波器
    plt.figure(figsize=(18, 6))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original F-shaped Filter')
    plt.colorbar()
    
    # 添加网格和数值标注
    for i in range(filterWeights.shape[0]):
        for j in range(filterWeights.shape[1]):
            value = filterWeights[i, j, 0]
            if value > 0:
                plt.text(j, i, f'{value:.0f}', ha='center', va='center', 
                        color='white', fontweight='bold')
    
    # 添加网格
    for i in range(filterWeights.shape[0] + 1):
        plt.axhline(i - 0.5, color='gray', linewidth=0.5, alpha=0.7)
    for j in range(filterWeights.shape[1] + 1):
        plt.axvline(j - 0.5, color='gray', linewidth=0.5, alpha=0.7)
    
    # 创建简单的映射权重
    mapSize = 2
    mapWeights = np.ones((mapSize, mapSize))
    
    plt.subplot(1, 3, 2)
    plt.imshow(mapWeights, cmap='hot', interpolation='nearest')
    plt.title('Map Weights (Uniform)')
    plt.colorbar()
    
    # 执行重构
    stride = 2
    poolSize = 1
    fullSize = 12
    
    print(f"重构参数:")
    print(f"  stride: {stride}")
    print(f"  poolSize: {poolSize}")
    print(f"  fullSize: {fullSize}")
    print(f"  mapSize: {mapSize}")
    
    # 调用修复后的plotReconstruction函数
    reconFilter = plotReconstruction(filterWeights, mapWeights, stride, poolSize, fullSize)
    
    # 显示重构结果
    plt.subplot(1, 3, 3)
    plt.imshow(reconFilter[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed Filter')
    plt.colorbar()
    
    # 添加网格
    for i in range(0, fullSize + 1, 2):
        plt.axhline(i - 0.5, color='gray', linewidth=0.3, alpha=0.5)
        plt.axvline(i - 0.5, color='gray', linewidth=0.3, alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('detailed_coordinate_test.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # 详细分析结果
    print(f"\n详细结果分析:")
    print(f"重构滤波器形状: {reconFilter.shape}")
    
    frame = reconFilter[:, :, 0]
    
    # 找到非零区域
    nonzero_mask = np.abs(frame) > 0.01
    nonzero_positions = np.where(nonzero_mask)
    
    if len(nonzero_positions[0]) > 0:
        min_row, max_row = np.min(nonzero_positions[0]), np.max(nonzero_positions[0])
        min_col, max_col = np.min(nonzero_positions[1]), np.max(nonzero_positions[1])
        
        print(f"非零区域边界:")
        print(f"  行范围: {min_row} - {max_row}")
        print(f"  列范围: {min_col} - {max_col}")
        
        # 检查F形特征
        # F形应该有：顶部横线、中间横线、左侧竖线
        
        # 检查顶部行
        top_row = frame[min_row, min_col:max_col+1]
        top_nonzero_count = np.sum(np.abs(top_row) > 0.01)
        
        # 检查左侧列
        left_col = frame[min_row:max_row+1, min_col]
        left_nonzero_count = np.sum(np.abs(left_col) > 0.01)
        
        # 检查中间是否有横线
        mid_row_idx = (min_row + max_row) // 2
        mid_row = frame[mid_row_idx, min_col:max_col+1]
        mid_nonzero_count = np.sum(np.abs(mid_row) > 0.01)
        
        print(f"F形特征分析:")
        print(f"  顶部横线非零元素: {top_nonzero_count}")
        print(f"  左侧竖线非零元素: {left_nonzero_count}")
        print(f"  中间横线非零元素: {mid_nonzero_count}")
        
        # 判断F形是否正确
        if (top_nonzero_count >= 3 and left_nonzero_count >= 3 and mid_nonzero_count >= 2):
            print("✅ F形特征保持正确 - 坐标系修复成功！")
        else:
            print("❌ F形特征可能不正确")
            
        # 显示重构结果的数值
        print(f"\n重构结果数值（非零区域）:")
        for i in range(min_row, min(min_row + 8, max_row + 1)):  # 只显示前8行
            row_str = ""
            for j in range(min_col, min(min_col + 8, max_col + 1)):  # 只显示前8列
                val = frame[i, j]
                if abs(val) > 0.01:
                    row_str += f"{val:6.2f} "
                else:
                    row_str += "  .    "
            print(f"  行{i:2d}: {row_str}")
    
    return reconFilter

def test_simple_horizontal_line():
    """测试简单的水平线"""
    print("\n" + "=" * 60)
    print("测试简单水平线")
    print("=" * 60)
    
    filterSize = 3
    numLags = 1
    
    # 创建水平线
    filterWeights = np.zeros((filterSize, filterSize, numLags))
    filterWeights[1, :, 0] = 1.0  # 中间行全部为1
    
    print("原始水平线滤波器:")
    print(filterWeights[:, :, 0])
    
    # 重构
    mapWeights = np.ones((2, 2))
    reconFilter = plotReconstruction(filterWeights, mapWeights, 1, 1, 8)
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 3, 1)
    plt.imshow(filterWeights[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Original Horizontal Line')
    plt.colorbar()
    
    plt.subplot(1, 3, 2)
    plt.imshow(reconFilter[:, :, 0], cmap='RdBu_r', interpolation='nearest')
    plt.title('Reconstructed')
    plt.colorbar()
    
    # 分析水平vs垂直特征
    frame = reconFilter[:, :, 0]
    horizontal_energy = np.sum(np.abs(np.diff(frame, axis=1)))
    vertical_energy = np.sum(np.abs(np.diff(frame, axis=0)))
    
    plt.subplot(1, 3, 3)
    plt.bar(['Horizontal', 'Vertical'], [horizontal_energy, vertical_energy])
    plt.title('Energy Analysis')
    plt.ylabel('Energy')
    
    print(f"能量分析:")
    print(f"  水平方向能量: {horizontal_energy:.2f}")
    print(f"  垂直方向能量: {vertical_energy:.2f}")
    
    if horizontal_energy > vertical_energy * 1.5:
        print("✅ 水平线保持水平方向")
    elif vertical_energy > horizontal_energy * 1.5:
        print("❌ 水平线变成了垂直方向（90度旋转）")
    else:
        print("⚠️  方向不明确")
    
    plt.tight_layout()
    plt.savefig('horizontal_line_test.png', dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("详细坐标系修复测试")
    print("=" * 60)
    
    # 测试F形重构
    test_detailed_reconstruction()
    
    # 测试简单水平线
    test_simple_horizontal_line()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("如果F形和水平线都保持正确方向，说明坐标系修复成功")
    print("=" * 60)

if __name__ == "__main__":
    main()
